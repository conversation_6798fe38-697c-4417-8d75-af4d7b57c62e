"""
任务管理器模块
"""
import os
import json
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional, Callable
from enum import Enum
from database import DatabaseManager


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待中
    RUNNING = "running"      # 运行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"  # 已取消


class Task:
    """任务类"""
    
    def __init__(self, task_id: str, name: str, description: str = "", 
                 model_path: str = "", parameters: Dict = None):
        self.task_id = task_id
        self.name = name
        self.description = description
        self.model_path = model_path
        self.parameters = parameters or {}
        self.status = TaskStatus.PENDING
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.progress = 0
        self.result = None
        self.error_message = None
        self.output_path = None
        
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'name': self.name,
            'description': self.description,
            'model_path': self.model_path,
            'parameters': self.parameters,
            'status': self.status.value,
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'progress': self.progress,
            'result': self.result,
            'error_message': self.error_message,
            'output_path': self.output_path
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Task':
        """从字典创建任务"""
        task = cls(
            task_id=data['task_id'],
            name=data['name'],
            description=data.get('description', ''),
            model_path=data.get('model_path', ''),
            parameters=data.get('parameters', {})
        )
        
        task.status = TaskStatus(data['status'])
        task.created_at = datetime.fromisoformat(data['created_at'])
        
        if data.get('started_at'):
            task.started_at = datetime.fromisoformat(data['started_at'])
        if data.get('completed_at'):
            task.completed_at = datetime.fromisoformat(data['completed_at'])
            
        task.progress = data.get('progress', 0)
        task.result = data.get('result')
        task.error_message = data.get('error_message')
        task.output_path = data.get('output_path')
        
        return task


class TaskManager:
    """任务管理器"""
    
    def __init__(self, history_file: str = "task_history.json"):
        self.tasks: Dict[str, Task] = {}
        self.running_tasks: Dict[str, threading.Thread] = {}
        self.history_file = history_file
        self.db = DatabaseManager()
        self.callbacks: Dict[str, List[Callable]] = {
            'task_started': [],
            'task_progress': [],
            'task_completed': [],
            'task_failed': [],
            'task_cancelled': []
        }

        # 检查并迁移历史任务
        self._check_and_migrate_tasks()

        # 从数据库加载历史任务
        self.load_history_from_db()

    def _check_and_migrate_tasks(self):
        """检查并迁移任务数据"""
        # 检查是否存在旧的JSON数据但数据库中没有数据
        db_tasks = self.db.get_all_tasks()
        if not db_tasks and os.path.exists(self.history_file):
            print("检测到旧的任务数据，正在迁移到数据库...")
            try:
                from data_migration import DataMigration
                migration = DataMigration()
                migration.migrate_tasks()
                print("任务数据迁移完成")
            except Exception as e:
                print(f"任务数据迁移失败: {e}")

    def load_history_from_db(self):
        """从数据库加载历史任务"""
        try:
            db_tasks = self.db.get_all_tasks()
            for task_data in db_tasks:
                task = Task.from_dict(task_data)
                self.tasks[task.task_id] = task
        except Exception as e:
            print(f"从数据库加载任务失败: {e}")

    def add_callback(self, event: str, callback: Callable):
        """添加回调函数"""
        if event in self.callbacks:
            self.callbacks[event].append(callback)
    
    def remove_callback(self, event: str, callback: Callable):
        """移除回调函数"""
        if event in self.callbacks and callback in self.callbacks[event]:
            self.callbacks[event].remove(callback)
    
    def _trigger_callback(self, event: str, task: Task):
        """触发回调函数"""
        for callback in self.callbacks.get(event, []):
            try:
                callback(task)
            except Exception as e:
                print(f"回调函数执行失败: {e}")
    
    def create_task(self, name: str, description: str = "", 
                   model_path: str = "", parameters: Dict = None) -> str:
        """创建新任务"""
        task_id = f"task_{int(time.time() * 1000)}"
        task = Task(task_id, name, description, model_path, parameters)
        self.tasks[task_id] = task
        return task_id
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务"""
        return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> List[Task]:
        """获取所有任务"""
        return list(self.tasks.values())
    
    def get_running_tasks(self) -> List[Task]:
        """获取运行中的任务"""
        return [task for task in self.tasks.values() if task.status == TaskStatus.RUNNING]
    
    def start_task(self, task_id: str, task_function: Callable, *args, **kwargs) -> bool:
        """启动任务"""
        task = self.get_task(task_id)
        if not task:
            return False
        
        if task.status != TaskStatus.PENDING:
            return False
        
        def run_task():
            try:
                task.status = TaskStatus.RUNNING
                task.started_at = datetime.now()
                self._trigger_callback('task_started', task)
                
                # 执行任务
                result = task_function(task, *args, **kwargs)
                
                task.status = TaskStatus.COMPLETED
                task.completed_at = datetime.now()
                task.result = result
                task.progress = 100
                
                self._trigger_callback('task_completed', task)
                
            except Exception as e:
                task.status = TaskStatus.FAILED
                task.completed_at = datetime.now()
                task.error_message = str(e)
                
                self._trigger_callback('task_failed', task)
            
            finally:
                # 从运行任务列表中移除
                if task_id in self.running_tasks:
                    del self.running_tasks[task_id]
                
                # 保存历史
                self.save_history()
        
        # 启动线程
        thread = threading.Thread(target=run_task, daemon=True)
        self.running_tasks[task_id] = thread
        thread.start()
        
        return True
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        task = self.get_task(task_id)
        if not task:
            return False
        
        if task.status == TaskStatus.RUNNING:
            # 注意：这里只是标记为取消，实际的线程停止需要在任务函数中检查状态
            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.now()
            
            self._trigger_callback('task_cancelled', task)
            
            # 从运行任务列表中移除
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
            
            self.save_history()
            return True
        
        return False
    
    def update_task_progress(self, task_id: str, progress: int, message: str = ""):
        """更新任务进度"""
        task = self.get_task(task_id)
        if task and task.status == TaskStatus.RUNNING:
            task.progress = max(0, min(100, progress))
            if message:
                task.description = message
            
            self._trigger_callback('task_progress', task)
    
    def clear_completed_tasks(self):
        """清除已完成的任务"""
        completed_task_ids = [
            task_id for task_id, task in self.tasks.items()
            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
        ]
        
        for task_id in completed_task_ids:
            del self.tasks[task_id]
        
        self.save_history()
    
    def save_history(self):
        """保存任务历史到数据库"""
        try:
            # 保存所有任务到数据库
            for task in self.tasks.values():
                task_data = task.to_dict()
                self.db.save_task(task_data)

            # 保持向后兼容，也保存到JSON
            history_data = {
                'tasks': [task.to_dict() for task in self.tasks.values()]
            }

            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"保存任务历史失败: {e}")
    
    def load_history(self):
        """加载任务历史"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history_data = json.load(f)
                
                for task_data in history_data.get('tasks', []):
                    task = Task.from_dict(task_data)
                    self.tasks[task.task_id] = task
                    
        except Exception as e:
            print(f"加载任务历史失败: {e}")
    
    def get_task_statistics(self) -> Dict:
        """获取任务统计信息"""
        stats = {
            'total': len(self.tasks),
            'pending': 0,
            'running': 0,
            'completed': 0,
            'failed': 0,
            'cancelled': 0
        }
        
        for task in self.tasks.values():
            stats[task.status.value] += 1
        
        return stats
