"""
简单的进度对话框测试
"""
import sys
import os
import tkinter as tk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import time
import threading

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_simple_progress():
    """测试简单的进度对话框"""
    # 创建主窗口
    style = ttk_bs.Style(theme="cosmo")
    root = style.master
    root.title("简单进度测试")
    root.geometry("400x300")
    
    # 主窗口居中
    from dialogs import center_window
    center_window(root)
    
    def mock_build_function(progress_callback=None):
        """模拟打包函数"""
        print("开始模拟打包...")
        
        steps = [
            (10, "正在准备打包环境..."),
            (20, "正在打包exe文件..."),
            (40, "正在编译Python代码..."),
            (60, "正在创建ZIP包..."),
            (80, "正在添加模型文件..."),
            (90, "正在清理临时文件..."),
            (95, "正在保存历史记录..."),
            (100, "打包完成！")
        ]
        
        for percent, message in steps:
            print(f"进度: {percent}% - {message}")
            if progress_callback:
                progress_callback(percent, message)
            time.sleep(1)  # 模拟耗时操作
        
        print("模拟打包完成")
        return {
            'success': True,
            'message': '模拟打包成功',
            'zip_path': 'test.zip'
        }
    
    def start_progress_test():
        """开始进度测试"""
        print("启动进度对话框...")
        try:
            from dialogs import ClientBuildProgressDialog
            dialog = ClientBuildProgressDialog(root, "测试客户端", mock_build_function)
            result = dialog.get_result()
            
            print(f"对话框结果: {result}")
            
            if result and result.get('success'):
                tk.messagebox.showinfo("成功", f"测试成功！\n{result.get('message')}")
            else:
                tk.messagebox.showerror("失败", f"测试失败：\n{result.get('message') if result else '无结果'}")
                
        except Exception as e:
            import traceback
            traceback.print_exc()
            tk.messagebox.showerror("错误", f"测试过程中发生错误：\n{e}")
    
    # 创建界面
    main_frame = ttk_bs.Frame(root, padding=20)
    main_frame.pack(fill=BOTH, expand=True)
    
    title_label = ttk_bs.Label(main_frame, text="简单进度对话框测试", 
                              font=("Arial", 14, "bold"))
    title_label.pack(pady=20)
    
    info_label = ttk_bs.Label(main_frame, 
                             text="这个测试将显示一个进度对话框\n"
                                  "模拟8秒的打包过程", 
                             font=("Arial", 10), justify=CENTER)
    info_label.pack(pady=10)
    
    ttk_bs.Button(main_frame, text="开始测试", 
                 command=start_progress_test, bootstyle=PRIMARY,
                 width=20).pack(pady=20)
    
    status_label = ttk_bs.Label(main_frame, 
                               text="点击按钮开始测试\n"
                                    "观察控制台输出和进度对话框", 
                               font=("Arial", 9), foreground="gray")
    status_label.pack(pady=10)
    
    print("简单进度测试程序已启动")
    
    root.mainloop()

if __name__ == "__main__":
    test_simple_progress()
