"""
调试客户端生成器的模型复制问题
"""
import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def debug_model_copy():
    """调试模型复制问题"""
    try:
        from model_manager import ModelManager
        from client_generator import ClientGenerator
        
        print("1. 初始化管理器...")
        model_manager = ModelManager()
        client_generator = ClientGenerator()
        
        print("2. 获取所有模型...")
        all_models = model_manager.get_all_models()
        print(f"   找到 {len(all_models)} 个模型")
        
        if not all_models:
            print("   没有找到任何模型，请先导入一些模型")
            return
        
        print("3. 检查每个模型的信息...")
        for i, (model_id, model_info) in enumerate(all_models.items()):
            print(f"\n模型 {i+1}: {model_id}")
            print(f"   名称: {model_info.get('name', 'N/A')}")
            print(f"   文件路径: {model_info.get('file_path', 'N/A')}")
            print(f"   是否加密: {model_info.get('is_encrypted', False)}")
            print(f"   原始文件名: {model_info.get('original_filename', 'N/A')}")
            print(f"   隐藏文件名: {model_info.get('hidden_filename', 'N/A')}")
            
            # 检查绝对路径
            try:
                abs_path = model_manager.get_absolute_path(model_info["file_path"])
                print(f"   绝对路径: {abs_path}")
                print(f"   文件存在: {os.path.exists(abs_path)}")
                
                if os.path.exists(abs_path):
                    file_size = os.path.getsize(abs_path)
                    print(f"   文件大小: {file_size} 字节")
                else:
                    print(f"   ❌ 文件不存在！")
                    
            except Exception as e:
                print(f"   ❌ 获取绝对路径失败: {e}")
        
        print("\n4. 测试模型复制逻辑...")
        # 选择第一个模型进行测试
        first_model_id = list(all_models.keys())[0]
        print(f"   测试模型ID: {first_model_id}")
        
        model_info = model_manager.get_model_info(first_model_id)
        if model_info:
            print(f"   模型信息获取成功")
            
            # 测试源文件路径
            source_file = model_manager.get_absolute_path(model_info["file_path"])
            print(f"   源文件路径: {source_file}")
            print(f"   源文件存在: {os.path.exists(source_file)}")
            
            # 测试目标文件名生成
            if model_info.get("is_encrypted", False):
                dest_filename = model_info.get("hidden_filename", f"model_{first_model_id}.dat")
                if not dest_filename.endswith('.enc'):
                    dest_filename += ".enc"
            else:
                dest_filename = model_info.get("original_filename", f"{model_info['name']}.fmw")
            
            print(f"   目标文件名: {dest_filename}")
            
        else:
            print(f"   ❌ 无法获取模型信息")
        
        print("\n5. 检查模型目录...")
        models_dir = model_manager.models_dir
        print(f"   模型目录: {models_dir}")
        print(f"   目录存在: {os.path.exists(models_dir)}")
        
        if os.path.exists(models_dir):
            files = os.listdir(models_dir)
            print(f"   目录中的文件: {files}")
        
    except Exception as e:
        print(f"调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_model_copy()
