# FME模型管理工具

一款功能强大的FME模型管理工具，提供模型导入、参数解析、加密保护、运行管理和分发客户端生成等功能。

## 主要功能

### 🔧 核心功能
- **FMW文件导入和管理** - 支持FMW文件的导入、分类和组织管理
- **参数自动解析** - 自动解析FMW文件中的用户参数，生成动态表单
- **模型加密保护** - 支持FMW文件加密，保护模型知识产权
- **模型运行管理** - 提供图形化界面运行FMW模型，支持参数配置
- **任务监控** - 实时监控模型运行状态，查看执行日志

### 📦 分发功能
- **客户端生成** - 一键生成独立的分发客户端
- **许可证管理** - 支持使用时间、次数、机器码等多种限制
- **打包分发** - 自动打包模型和客户端，便于分发部署

### 🎨 界面特性
- **现代化界面** - 基于ttkbootstrap的现代化UI设计
- **多主题支持** - 支持多种界面主题切换
- **响应式布局** - 自适应窗口大小的响应式界面

## 系统要求

- **操作系统**: Windows 7/8/10/11
- **Python**: 3.7 或更高版本
- **FME**: 需要安装FME Desktop（用于运行模型）
- **内存**: 建议4GB以上
- **磁盘空间**: 至少500MB可用空间

## 安装说明

### 方式一：使用启动脚本（推荐）

1. 下载并解压工具包到任意目录
2. 双击运行 `start.bat` 脚本
3. 脚本会自动创建虚拟环境并安装依赖
4. 首次运行可能需要几分钟时间安装依赖包

### 方式二：手动安装

1. 确保已安装Python 3.7+
2. 打开命令行，进入工具目录
3. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```
4. 运行程序：
   ```bash
   python main.py
   ```

## 快速开始

### 1. 首次配置

启动程序后，首先进行基本配置：

1. 点击菜单 `文件 -> 设置`
2. 在"基本设置"选项卡中设置FME可执行文件路径
3. 在"路径设置"选项卡中配置工作目录
4. 点击"确定"保存设置

### 2. 导入模型

1. 点击工具栏的"导入模型"按钮
2. 选择要导入的FMW文件
3. 填写模型名称、描述和分类
4. 点击"确定"完成导入

### 3. 运行模型

1. 在模型列表中选择要运行的模型
2. 在"参数设置"选项卡中配置模型参数
3. 点击"运行模型"按钮
4. 在"运行日志"选项卡中查看执行进度

### 4. 创建分发客户端

1. 点击菜单 `分发 -> 创建客户端`
2. 填写客户端基本信息
3. 选择要包含的模型
4. 配置许可证限制（可选）
5. 点击"创建客户端"生成分发包

## 功能详解

### 模型管理

- **导入模型**: 支持导入FMW文件，自动解析参数信息
- **模型分类**: 支持自定义分类，便于组织管理
- **搜索筛选**: 支持按名称、分类搜索模型
- **模型信息**: 显示详细的模型信息和参数列表

### 参数解析

工具能够自动解析FMW文件中的用户参数，支持以下参数类型：

- **文本输入**: 普通文本、数字、密码等
- **文件选择**: 支持文件和文件夹选择
- **下拉选择**: 单选和多选下拉框
- **日期时间**: 日期时间选择器
- **颜色选择**: 颜色选择器
- **条件显示**: 支持参数间的条件显示逻辑

### 加密保护

- **文件加密**: 使用AES加密算法保护FMW文件
- **密钥管理**: 自动生成和管理加密密钥
- **透明解密**: 运行时自动解密，用户无感知

### 分发客户端

生成的分发客户端具有以下特性：

- **独立运行**: 无需安装主程序，独立运行
- **许可证保护**: 支持多种使用限制
- **简化界面**: 针对最终用户优化的简洁界面
- **自动更新**: 支持许可证使用次数自动更新

## 许可证类型

### 时间限制
- 设置客户端过期时间
- 过期后自动禁用

### 次数限制
- 设置最大使用次数
- 达到上限后自动禁用

### 机器码限制
- 绑定特定机器
- 防止随意复制使用

## 目录结构

```
fme管理工具/
├── main.py                 # 主程序入口
├── main_window.py          # 主窗口界面
├── config.py               # 配置管理
├── model_manager.py        # 模型管理
├── fmw_runner.py          # FMW运行器
├── encryption.py          # 加密模块
├── client_generator.py    # 客户端生成器
├── dialogs.py             # 对话框模块
├── parse_fmw.py           # FMW解析模块
├── client_template.py     # 客户端模板
├── requirements.txt       # 依赖包列表
├── start.bat             # 启动脚本
└── README.md             # 说明文档
```

## 常见问题

### Q: 程序启动失败怎么办？
A: 请检查以下几点：
1. 确保Python版本为3.7或更高
2. 检查是否正确安装了所有依赖包
3. 查看fme_tool.log日志文件获取详细错误信息

### Q: 无法运行FMW模型？
A: 请检查：
1. FME路径是否正确配置
2. FMW文件是否完整
3. 输入参数是否正确

### Q: 生成的客户端无法运行？
A: 请确保：
1. 目标机器已安装FME
2. 许可证未过期且未超出使用限制
3. 机器码匹配（如果启用了机器码限制）

### Q: 如何备份模型数据？
A: 模型数据存储在工作目录的models文件夹中，定期备份该文件夹即可。

## 技术支持

如果您在使用过程中遇到问题，请：

1. 查看程序日志文件 `fme_tool.log`
2. 检查本文档的常见问题部分
3. 联系技术支持

## 更新日志

### v1.0.0 (2024-08-12)
- 首次发布
- 实现基本的模型管理功能
- 支持FMW参数解析和运行
- 提供模型加密保护
- 支持分发客户端生成

## 许可协议

本软件仅供学习和研究使用，请勿用于商业用途。
