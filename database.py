"""
数据库管理模块
"""
import sqlite3
import os
import json
from datetime import datetime
from typing import Dict, List, Optional, Any


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "d.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库，创建所有必要的表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建模型表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS models (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    project TEXT,
                    category TEXT DEFAULT '默认',
                    original_filename TEXT,
                    hidden_filename TEXT,
                    file_path TEXT NOT NULL,
                    original_file_path TEXT,
                    file_hash TEXT,
                    file_size INTEGER,
                    parameters TEXT,  -- JSON格式存储参数
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    is_encrypted BOOLEAN DEFAULT 0,
                    encrypted_file_path TEXT,
                    usage_count INTEGER DEFAULT 0,
                    last_used TEXT
                )
            ''')
            
            # 创建任务表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS tasks (
                    task_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    model_path TEXT,
                    parameters TEXT,  -- JSON格式存储参数
                    status TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    started_at TEXT,
                    completed_at TEXT,
                    progress INTEGER DEFAULT 0,
                    result TEXT,
                    error_message TEXT,
                    output_path TEXT
                )
            ''')
            
            # 创建客户端表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS clients (
                    client_id TEXT PRIMARY KEY,
                    client_name TEXT NOT NULL,
                    project TEXT,
                    user_name TEXT,
                    contact TEXT,
                    description TEXT,
                    models TEXT,  -- JSON格式存储模型列表
                    fme_path TEXT,
                    created_at TEXT NOT NULL,
                    zip_path TEXT
                )
            ''')
            
            # 创建客户端创建历史表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS client_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    created_at TEXT NOT NULL,
                    client_name TEXT NOT NULL,
                    project TEXT,
                    user_name TEXT,
                    contact TEXT,
                    description TEXT,
                    model_count INTEGER,
                    status TEXT NOT NULL
                )
            ''')
            
            # 创建许可证生成历史表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS license_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    created_at TEXT NOT NULL,
                    client_name TEXT NOT NULL,
                    project TEXT,
                    user_name TEXT,
                    contact TEXT,
                    description TEXT,
                    model_count INTEGER,
                    status TEXT NOT NULL
                )
            ''')
            
            # 创建应用配置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS app_config (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            ''')
            
            conn.commit()
    
    def execute_query(self, query: str, params: tuple = ()) -> List[Dict]:
        """执行查询并返回结果"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """执行更新操作并返回影响的行数"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            return cursor.rowcount
    
    def execute_insert(self, query: str, params: tuple = ()) -> str:
        """执行插入操作并返回最后插入的ID"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            return cursor.lastrowid
    
    # 模型相关操作
    def save_model(self, model_data: Dict) -> bool:
        """保存模型信息"""
        try:
            query = '''
                INSERT OR REPLACE INTO models 
                (id, name, description, project, category, original_filename, 
                 hidden_filename, file_path, original_file_path, file_hash, 
                 file_size, parameters, created_at, updated_at, is_encrypted, 
                 encrypted_file_path, usage_count, last_used)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            params = (
                model_data.get('id'),
                model_data.get('name'),
                model_data.get('description'),
                model_data.get('project'),
                model_data.get('category', '默认'),
                model_data.get('original_filename'),
                model_data.get('hidden_filename'),
                model_data.get('file_path'),
                model_data.get('original_file_path'),
                model_data.get('file_hash'),
                model_data.get('file_size'),
                json.dumps(model_data.get('parameters', [])),
                model_data.get('created_at'),
                model_data.get('updated_at'),
                model_data.get('is_encrypted', False),
                model_data.get('encrypted_file_path'),
                model_data.get('usage_count', 0),
                model_data.get('last_used')
            )
            self.execute_update(query, params)
            return True
        except Exception as e:
            print(f"保存模型失败: {e}")
            return False
    
    def get_model(self, model_id: str) -> Optional[Dict]:
        """获取单个模型信息"""
        query = "SELECT * FROM models WHERE id = ?"
        results = self.execute_query(query, (model_id,))
        if results:
            model = results[0]
            # 解析JSON字段
            if model['parameters']:
                model['parameters'] = json.loads(model['parameters'])
            return model
        return None
    
    def get_all_models(self) -> Dict[str, Dict]:
        """获取所有模型"""
        query = "SELECT * FROM models ORDER BY created_at DESC"
        results = self.execute_query(query)
        models = {}
        for row in results:
            model = dict(row)
            # 解析JSON字段
            if model['parameters']:
                model['parameters'] = json.loads(model['parameters'])
            models[model['id']] = model
        return models
    
    def delete_model(self, model_id: str) -> bool:
        """删除模型"""
        try:
            query = "DELETE FROM models WHERE id = ?"
            affected = self.execute_update(query, (model_id,))
            return affected > 0
        except Exception as e:
            print(f"删除模型失败: {e}")
            return False
    
    def update_model_usage(self, model_id: str):
        """更新模型使用次数"""
        query = '''
            UPDATE models 
            SET usage_count = usage_count + 1, last_used = ? 
            WHERE id = ?
        '''
        self.execute_update(query, (datetime.now().isoformat(), model_id))
    
    # 任务相关操作
    def save_task(self, task_data: Dict) -> bool:
        """保存任务信息"""
        try:
            query = '''
                INSERT OR REPLACE INTO tasks 
                (task_id, name, description, model_path, parameters, status, 
                 created_at, started_at, completed_at, progress, result, 
                 error_message, output_path)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            params = (
                task_data.get('task_id'),
                task_data.get('name'),
                task_data.get('description'),
                task_data.get('model_path'),
                json.dumps(task_data.get('parameters', {})),
                task_data.get('status'),
                task_data.get('created_at'),
                task_data.get('started_at'),
                task_data.get('completed_at'),
                task_data.get('progress', 0),
                task_data.get('result'),
                task_data.get('error_message'),
                task_data.get('output_path')
            )
            self.execute_update(query, params)
            return True
        except Exception as e:
            print(f"保存任务失败: {e}")
            return False
    
    def get_all_tasks(self) -> List[Dict]:
        """获取所有任务"""
        query = "SELECT * FROM tasks ORDER BY created_at DESC"
        results = self.execute_query(query)
        for task in results:
            if task['parameters']:
                task['parameters'] = json.loads(task['parameters'])
        return results
    
    # 客户端相关操作
    def save_client(self, client_data: Dict) -> bool:
        """保存客户端信息"""
        try:
            query = '''
                INSERT OR REPLACE INTO clients 
                (client_id, client_name, project, user_name, contact, 
                 description, models, fme_path, created_at, zip_path)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            params = (
                client_data.get('client_id'),
                client_data.get('client_name'),
                client_data.get('project'),
                client_data.get('user'),
                client_data.get('contact'),
                client_data.get('description'),
                json.dumps(client_data.get('models', [])),
                client_data.get('fme_path'),
                client_data.get('created_at'),
                client_data.get('zip_path')
            )
            self.execute_update(query, params)
            return True
        except Exception as e:
            print(f"保存客户端失败: {e}")
            return False
    
    def get_all_clients(self) -> List[Dict]:
        """获取所有客户端"""
        query = "SELECT * FROM clients ORDER BY created_at DESC"
        results = self.execute_query(query)
        for client in results:
            if client['models']:
                client['models'] = json.loads(client['models'])
        return results
    
    # 历史记录相关操作
    def save_client_history(self, history_data: Dict) -> bool:
        """保存客户端创建历史"""
        try:
            query = '''
                INSERT INTO client_history 
                (created_at, client_name, project, user_name, contact, 
                 description, model_count, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            '''
            params = (
                history_data.get('created_at', datetime.now().isoformat()),
                history_data.get('client_name'),
                history_data.get('project'),
                history_data.get('user'),
                history_data.get('contact'),
                history_data.get('description'),
                history_data.get('model_count', 0),
                history_data.get('status')
            )
            self.execute_insert(query, params)
            return True
        except Exception as e:
            print(f"保存客户端历史失败: {e}")
            return False
    
    def get_client_history(self) -> List[Dict]:
        """获取客户端创建历史"""
        query = "SELECT * FROM client_history ORDER BY created_at DESC LIMIT 100"
        return self.execute_query(query)
    
    def save_license_history(self, history_data: Dict) -> bool:
        """保存许可证生成历史"""
        try:
            query = '''
                INSERT INTO license_history 
                (created_at, client_name, project, user_name, contact, 
                 description, model_count, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            '''
            params = (
                history_data.get('created_at', datetime.now().isoformat()),
                history_data.get('client_name'),
                history_data.get('project'),
                history_data.get('user'),
                history_data.get('contact'),
                history_data.get('description'),
                history_data.get('model_count', 0),
                history_data.get('status')
            )
            self.execute_insert(query, params)
            return True
        except Exception as e:
            print(f"保存许可证历史失败: {e}")
            return False
    
    def get_license_history(self) -> List[Dict]:
        """获取许可证生成历史"""
        query = "SELECT * FROM license_history ORDER BY created_at DESC LIMIT 100"
        return self.execute_query(query)
    
    # 配置相关操作
    def set_config(self, key: str, value: Any) -> bool:
        """设置配置项"""
        try:
            query = '''
                INSERT OR REPLACE INTO app_config (key, value, updated_at)
                VALUES (?, ?, ?)
            '''
            params = (key, json.dumps(value), datetime.now().isoformat())
            self.execute_update(query, params)
            return True
        except Exception as e:
            print(f"设置配置失败: {e}")
            return False
    
    def get_config(self, key: str, default=None) -> Any:
        """获取配置项"""
        query = "SELECT value FROM app_config WHERE key = ?"
        results = self.execute_query(query, (key,))
        if results:
            return json.loads(results[0]['value'])
        return default
    
    def get_all_config(self) -> Dict:
        """获取所有配置"""
        query = "SELECT key, value FROM app_config"
        results = self.execute_query(query)
        config = {}
        for row in results:
            config[row['key']] = json.loads(row['value'])
        return config
