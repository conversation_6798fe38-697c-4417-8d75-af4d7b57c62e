"""
测试客户端创建进度对话框
"""
import sys
import os
import tkinter as tk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_client_creation_with_progress():
    """测试带进度的客户端创建"""
    # 创建主窗口
    style = ttk_bs.Style(theme="cosmo")
    root = style.master
    root.title("客户端创建进度测试")
    root.geometry("600x400")
    
    # 主窗口居中
    from dialogs import center_window
    center_window(root)
    
    def start_client_creation():
        """开始客户端创建"""
        try:
            from model_manager import ModelManager
            from client_generator import ClientGenerator
            
            print("初始化管理器...")
            model_manager = ModelManager()
            client_generator = ClientGenerator()
            
            print("获取模型...")
            all_models = model_manager.get_all_models()
            if not all_models:
                tk.messagebox.showerror("错误", "没有找到任何模型")
                return
            
            # 选择第一个模型
            selected_models = [list(all_models.keys())[0]]
            print(f"选择的模型: {selected_models}")
            
            # 客户端信息
            client_info = {
                'name': '进度测试客户端',
                'project': '进度测试项目',
                'user': '测试用户',
                'contact': '<EMAIL>',
                'description': '这是一个进度测试客户端'
            }
            
            print("开始创建客户端（带进度对话框）...")
            
            # 这将显示进度对话框
            success, message, zip_path = client_generator.generate_client(
                selected_models, client_info
            )
            
            if success:
                tk.messagebox.showinfo("成功", f"客户端创建成功！\n{message}")
            else:
                tk.messagebox.showerror("失败", f"客户端创建失败：\n{message}")
                
        except Exception as e:
            import traceback
            traceback.print_exc()
            tk.messagebox.showerror("错误", f"创建过程中发生错误：\n{e}")
    
    def test_progress_dialog_only():
        """仅测试进度对话框"""
        from dialogs import ClientBuildProgressDialog
        import time
        
        def mock_build_function(progress_callback=None):
            """模拟打包函数"""
            steps = [
                (10, "正在准备打包环境..."),
                (20, "正在打包exe文件..."),
                (40, "正在编译Python代码..."),
                (60, "正在创建ZIP包..."),
                (80, "正在添加模型文件..."),
                (90, "正在清理临时文件..."),
                (95, "正在保存历史记录..."),
                (100, "打包完成！")
            ]
            
            for percent, message in steps:
                if progress_callback:
                    progress_callback(percent, message)
                time.sleep(0.5)  # 模拟耗时操作
            
            return {
                'success': True,
                'message': '模拟打包成功',
                'zip_path': 'test.zip'
            }
        
        # 显示进度对话框
        dialog = ClientBuildProgressDialog(root, "测试客户端", mock_build_function)
        result = dialog.get_result()
        
        if result:
            tk.messagebox.showinfo("结果", f"打包结果：\n{result}")
    
    # 创建界面
    main_frame = ttk_bs.Frame(root, padding=20)
    main_frame.pack(fill=BOTH, expand=True)
    
    title_label = ttk_bs.Label(main_frame, text="客户端创建进度测试", 
                              font=("Arial", 16, "bold"))
    title_label.pack(pady=20)
    
    info_label = ttk_bs.Label(main_frame, 
                             text="测试客户端创建过程中的进度显示\n"
                                  "如果进度对话框卡住不动，说明有问题", 
                             font=("Arial", 10), justify=CENTER)
    info_label.pack(pady=10)
    
    button_frame = ttk_bs.Frame(main_frame)
    button_frame.pack(pady=30)
    
    ttk_bs.Button(button_frame, text="测试真实客户端创建", 
                 command=start_client_creation, bootstyle=PRIMARY,
                 width=25).pack(pady=10)
    
    ttk_bs.Button(button_frame, text="测试进度对话框（模拟）", 
                 command=test_progress_dialog_only, bootstyle=SECONDARY,
                 width=25).pack(pady=10)
    
    status_label = ttk_bs.Label(main_frame, 
                               text="点击按钮开始测试\n"
                                    "观察进度对话框是否正常显示和更新", 
                               font=("Arial", 9), foreground="gray")
    status_label.pack(pady=20)
    
    print("客户端创建进度测试程序已启动")
    print("点击按钮测试进度对话框")
    
    root.mainloop()

if __name__ == "__main__":
    test_client_creation_with_progress()
