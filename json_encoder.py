"""
JSON文件编码工具
用于保护敏感的JSON配置文件，防止用户直接查看内容
"""
import json
import base64
import os
from cryptography.fernet import Fernet
import hashlib


class JsonEncoder:
    """JSON文件编码器"""
    
    def __init__(self):
        self.key = self._get_or_create_key()
        self.cipher = Fernet(self.key)
    
    def _get_or_create_key(self):
        """获取或创建加密密钥"""
        # 使用固定的密钥种子
        key_string = "FME_JSON_ENCODER_KEY_2024_SECURE"
        key_bytes = hashlib.sha256(key_string.encode()).digest()
        return base64.urlsafe_b64encode(key_bytes)
    
    def encode_json_file(self, json_data, output_file):
        """编码JSON数据到文件"""
        try:
            # 将JSON数据转换为字符串
            json_string = json.dumps(json_data, ensure_ascii=False, indent=2)
            
            # 加密JSON字符串
            encrypted_data = self.cipher.encrypt(json_string.encode('utf-8'))
            
            # Base64编码
            encoded_data = base64.b64encode(encrypted_data).decode('utf-8')
            
            # 保存到文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(encoded_data)
            
            return True
            
        except Exception as e:
            print(f"编码JSON文件失败: {e}")
            return False
    
    def decode_json_file(self, input_file):
        """从文件解码JSON数据"""
        try:
            if not os.path.exists(input_file):
                return None
            
            # 读取文件内容
            with open(input_file, 'r', encoding='utf-8') as f:
                encoded_data = f.read().strip()
            
            # Base64解码
            encrypted_data = base64.b64decode(encoded_data)
            
            # 解密数据
            decrypted_data = self.cipher.decrypt(encrypted_data)
            
            # 解析JSON
            json_data = json.loads(decrypted_data.decode('utf-8'))
            
            return json_data
            
        except Exception as e:
            print(f"解码JSON文件失败: {e}")
            return None
    
    def save_encoded_json(self, json_data, file_path):
        """保存编码的JSON文件"""
        return self.encode_json_file(json_data, file_path)
    
    def load_encoded_json(self, file_path):
        """加载编码的JSON文件"""
        return self.decode_json_file(file_path)
    
    def migrate_plain_json_to_encoded(self, plain_json_file, encoded_json_file):
        """将普通JSON文件迁移为编码文件"""
        try:
            if not os.path.exists(plain_json_file):
                return False
            
            # 读取普通JSON文件
            with open(plain_json_file, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            # 保存为编码文件
            success = self.encode_json_file(json_data, encoded_json_file)
            
            if success:
                # 删除原始文件
                os.remove(plain_json_file)
                print(f"JSON文件已迁移: {plain_json_file} -> {encoded_json_file}")
            
            return success
            
        except Exception as e:
            print(f"迁移JSON文件失败: {e}")
            return False


class SecureJsonManager:
    """安全JSON管理器"""
    
    def __init__(self):
        self.encoder = JsonEncoder()
    
    def save_config(self, config_data, config_name):
        """保存配置文件"""
        config_file = f"{config_name}.dat"
        return self.encoder.save_encoded_json(config_data, config_file)
    
    def load_config(self, config_name, default_config=None):
        """加载配置文件"""
        config_file = f"{config_name}.dat"
        config_data = self.encoder.load_encoded_json(config_file)
        
        if config_data is None:
            # 尝试加载普通JSON文件并迁移
            plain_file = f"{config_name}.json"
            if os.path.exists(plain_file):
                print(f"发现普通JSON文件，正在迁移: {plain_file}")
                if self.encoder.migrate_plain_json_to_encoded(plain_file, config_file):
                    config_data = self.encoder.load_encoded_json(config_file)
            
            # 如果仍然没有数据，使用默认配置
            if config_data is None and default_config is not None:
                config_data = default_config
                self.save_config(config_data, config_name)
        
        return config_data
    
    def update_config(self, config_name, updates):
        """更新配置文件"""
        config_data = self.load_config(config_name, {})
        config_data.update(updates)
        return self.save_config(config_data, config_name)
    
    def get_config_value(self, config_name, key, default_value=None):
        """获取配置值"""
        config_data = self.load_config(config_name, {})
        return config_data.get(key, default_value)
    
    def set_config_value(self, config_name, key, value):
        """设置配置值"""
        config_data = self.load_config(config_name, {})
        config_data[key] = value
        return self.save_config(config_data, config_name)


def migrate_all_json_files():
    """迁移所有JSON文件为编码格式"""
    encoder = JsonEncoder()
    
    # 需要迁移的JSON文件列表
    json_files = [
        "models.json",
        "client_config.json",
        "fme_config.json",
        "app_config.json"
    ]
    
    migrated_count = 0
    
    for json_file in json_files:
        if os.path.exists(json_file):
            encoded_file = json_file.replace('.json', '.dat')
            if encoder.migrate_plain_json_to_encoded(json_file, encoded_file):
                migrated_count += 1
    
    print(f"迁移完成，共迁移 {migrated_count} 个JSON文件")
    return migrated_count


if __name__ == "__main__":
    # 测试编码器
    print("=== JSON编码器测试 ===")
    
    encoder = JsonEncoder()
    
    # 测试数据
    test_data = {
        "app_name": "FME管理工具",
        "version": "1.0.0",
        "models": [
            {"id": "001", "name": "测试模型1"},
            {"id": "002", "name": "测试模型2"}
        ],
        "settings": {
            "auto_backup": True,
            "max_models": 100
        }
    }
    
    # 编码测试
    print("1. 编码测试...")
    success = encoder.encode_json_file(test_data, "test_encoded.dat")
    print(f"编码结果: {'成功' if success else '失败'}")
    
    # 解码测试
    print("2. 解码测试...")
    decoded_data = encoder.decode_json_file("test_encoded.dat")
    if decoded_data:
        print("解码成功")
        print(f"原始数据: {test_data}")
        print(f"解码数据: {decoded_data}")
        print(f"数据一致: {test_data == decoded_data}")
    else:
        print("解码失败")
    
    # 清理测试文件
    if os.path.exists("test_encoded.dat"):
        os.remove("test_encoded.dat")
    
    print("\n=== 安全JSON管理器测试 ===")
    
    manager = SecureJsonManager()
    
    # 保存配置
    config_data = {
        "fme_path": "C:\\Program Files\\FME\\fme.exe",
        "output_dir": "C:\\Output",
        "auto_encrypt": True
    }
    
    print("3. 保存配置测试...")
    success = manager.save_config(config_data, "test_config")
    print(f"保存结果: {'成功' if success else '失败'}")
    
    # 加载配置
    print("4. 加载配置测试...")
    loaded_config = manager.load_config("test_config")
    if loaded_config:
        print("加载成功")
        print(f"配置数据: {loaded_config}")
    else:
        print("加载失败")
    
    # 清理测试文件
    if os.path.exists("test_config.dat"):
        os.remove("test_config.dat")
    
    print("\n测试完成！")
