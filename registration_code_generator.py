"""
注册码生成器
为客户端生成注册码
"""
import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import base64
import hashlib
from datetime import datetime, timedelta

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

class RegistrationCodeGenerator:
    """注册码生成器"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("客户端注册码生成器")
        self.root.geometry("700x500")
        
        # 居中显示
        self.center_window()
        self.setup_ui()
    
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (350)
        y = (self.root.winfo_screenheight() // 2) - (250)
        self.root.geometry(f"700x500+{x}+{y}")
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="客户端注册码生成器", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 机器码输入
        machine_frame = ttk.LabelFrame(main_frame, text="机器码信息", padding=15)
        machine_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(machine_frame, text="客户端机器码:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))
        
        self.machine_id_var = tk.StringVar()
        machine_entry = ttk.Entry(machine_frame, textvariable=self.machine_id_var, 
                                 font=("Consolas", 10), width=50)
        machine_entry.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(machine_frame, text="请输入客户端提供的机器码", 
                 font=("Arial", 9), foreground="gray").pack(anchor=tk.W)
        
        # 许可证设置
        license_frame = ttk.LabelFrame(main_frame, text="许可证设置", padding=15)
        license_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 客户端名称
        name_frame = ttk.Frame(license_frame)
        name_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(name_frame, text="客户端名称:").pack(side=tk.LEFT)
        self.client_name_var = tk.StringVar(value="FME客户端")
        ttk.Entry(name_frame, textvariable=self.client_name_var, width=30).pack(side=tk.LEFT, padx=(10, 0))
        
        # 过期时间
        expire_frame = ttk.Frame(license_frame)
        expire_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.enable_expire_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(expire_frame, text="启用过期时间", 
                       variable=self.enable_expire_var,
                       command=self.toggle_expire).pack(side=tk.LEFT)
        
        ttk.Label(expire_frame, text="天数:").pack(side=tk.LEFT, padx=(20, 0))
        self.expire_days_var = tk.StringVar(value="365")
        self.expire_days_entry = ttk.Entry(expire_frame, textvariable=self.expire_days_var, width=10)
        self.expire_days_entry.pack(side=tk.LEFT, padx=(5, 0))
        
        # 使用次数
        usage_frame = ttk.Frame(license_frame)
        usage_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.enable_usage_var = tk.BooleanVar()
        ttk.Checkbutton(usage_frame, text="启用使用次数限制", 
                       variable=self.enable_usage_var,
                       command=self.toggle_usage).pack(side=tk.LEFT)
        
        ttk.Label(usage_frame, text="次数:").pack(side=tk.LEFT, padx=(20, 0))
        self.max_uses_var = tk.StringVar(value="1000")
        self.max_uses_entry = ttk.Entry(usage_frame, textvariable=self.max_uses_var, width=10)
        self.max_uses_entry.pack(side=tk.LEFT, padx=(5, 0))
        self.max_uses_entry.config(state=tk.DISABLED)
        
        # 生成按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(15, 0))
        
        ttk.Button(button_frame, text="生成注册码", 
                  command=self.generate_code).pack(side=tk.LEFT)
        
        ttk.Button(button_frame, text="清空", 
                  command=self.clear_form).pack(side=tk.LEFT, padx=(10, 0))
        
        ttk.Button(button_frame, text="退出", 
                  command=self.root.quit).pack(side=tk.RIGHT)
        
        # 结果显示
        result_frame = ttk.LabelFrame(main_frame, text="生成的注册码", padding=15)
        result_frame.pack(fill=tk.BOTH, expand=True, pady=(15, 0))
        
        self.result_text = tk.Text(result_frame, wrap=tk.WORD, font=("Consolas", 9))
        result_scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=result_scrollbar.set)
        
        self.result_text.pack(side="left", fill="both", expand=True)
        result_scrollbar.pack(side="right", fill="y")
        
        # 复制按钮
        copy_frame = ttk.Frame(result_frame)
        copy_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(copy_frame, text="复制注册码", 
                  command=self.copy_code).pack(side=tk.LEFT)
        
        ttk.Button(copy_frame, text="保存到文件", 
                  command=self.save_code).pack(side=tk.LEFT, padx=(10, 0))
    
    def toggle_expire(self):
        """切换过期时间设置"""
        if self.enable_expire_var.get():
            self.expire_days_entry.config(state=tk.NORMAL)
        else:
            self.expire_days_entry.config(state=tk.DISABLED)
    
    def toggle_usage(self):
        """切换使用次数设置"""
        if self.enable_usage_var.get():
            self.max_uses_entry.config(state=tk.NORMAL)
        else:
            self.max_uses_entry.config(state=tk.DISABLED)
    
    def generate_code(self):
        """生成注册码"""
        machine_id = self.machine_id_var.get().strip()
        if not machine_id:
            messagebox.showwarning("警告", "请输入客户端机器码")
            return
        
        if len(machine_id) != 32:  # MD5哈希长度
            messagebox.showwarning("警告", "机器码格式不正确（应为32位十六进制字符串）")
            return
        
        try:
            # 构建许可证数据
            license_data = {
                "license_id": self.generate_license_id(),
                "client_name": self.client_name_var.get(),
                "created_at": datetime.now().isoformat(),
                "allowed_machines": [machine_id],
                "current_uses": 0,
                "is_active": True
            }
            
            # 添加过期时间
            if self.enable_expire_var.get():
                try:
                    days = int(self.expire_days_var.get())
                    expire_date = datetime.now() + timedelta(days=days)
                    license_data["expire_date"] = expire_date.isoformat()
                except ValueError:
                    messagebox.showerror("错误", "过期天数必须是整数")
                    return
            
            # 添加使用次数限制
            if self.enable_usage_var.get():
                try:
                    max_uses = int(self.max_uses_var.get())
                    license_data["max_uses"] = max_uses
                except ValueError:
                    messagebox.showerror("错误", "使用次数必须是整数")
                    return
            
            # 生成注册码（Base64编码的JSON）
            license_json = json.dumps(license_data, ensure_ascii=False)
            registration_code = base64.b64encode(license_json.encode()).decode()
            
            # 显示结果
            result_text = f"""注册码生成成功！

许可证信息:
• 许可证ID: {license_data['license_id']}
• 客户端名称: {license_data['client_name']}
• 绑定机器码: {machine_id}
• 创建时间: {license_data['created_at']}
• 过期时间: {license_data.get('expire_date', '无限制')}
• 最大使用次数: {license_data.get('max_uses', '无限制')}

注册码:
{registration_code}

使用说明:
1. 将上述注册码提供给客户端用户
2. 用户在客户端中输入此注册码进行激活
3. 注册码与机器码绑定，只能在指定机器上使用
"""
            
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(1.0, result_text)
            
            # 保存注册码用于复制
            self.current_code = registration_code
            
        except Exception as e:
            messagebox.showerror("错误", f"生成注册码失败: {e}")
    
    def generate_license_id(self):
        """生成许可证ID"""
        import uuid
        return str(uuid.uuid4())
    
    def copy_code(self):
        """复制注册码"""
        if hasattr(self, 'current_code'):
            self.root.clipboard_clear()
            self.root.clipboard_append(self.current_code)
            messagebox.showinfo("提示", "注册码已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "请先生成注册码")
    
    def save_code(self):
        """保存注册码到文件"""
        if not hasattr(self, 'current_code'):
            messagebox.showwarning("警告", "请先生成注册码")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="保存注册码",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialvalue=f"registration_code_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.result_text.get(1.0, tk.END))
                messagebox.showinfo("成功", f"注册码已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存注册码失败: {e}")
    
    def clear_form(self):
        """清空表单"""
        self.machine_id_var.set("")
        self.client_name_var.set("FME客户端")
        self.enable_expire_var.set(True)
        self.expire_days_var.set("365")
        self.enable_usage_var.set(False)
        self.max_uses_var.set("1000")
        
        self.toggle_expire()
        self.toggle_usage()
        
        self.result_text.delete(1.0, tk.END)
        
        if hasattr(self, 'current_code'):
            delattr(self, 'current_code')
    
    def run(self):
        """运行生成器"""
        self.root.mainloop()

if __name__ == "__main__":
    try:
        print("启动注册码生成器...")
        generator = RegistrationCodeGenerator()
        generator.run()
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
