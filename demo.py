"""
FME模型管理工具演示脚本
"""
import os
import sys
import tempfile
import shutil
from datetime import datetime, timedelta

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def demo_basic_functionality():
    """演示基本功能"""
    print("=" * 60)
    print("FME模型管理工具功能演示")
    print("=" * 60)
    
    # 1. 配置管理演示
    print("\n1. 配置管理演示")
    print("-" * 30)
    
    from config import config
    print(f"应用名称: {config.get('app_name')}")
    print(f"版本: {config.get('app_version')}")
    print(f"工作目录: {config.get('workspace_dir')}")
    print(f"主题: {config.get('theme')}")
    
    # 确保目录存在
    config.ensure_directories()
    print("✓ 工作目录已创建")
    
    # 2. 加密功能演示
    print("\n2. 加密功能演示")
    print("-" * 30)
    
    from encryption import FMWEncryption, LicenseManager
    
    # 创建测试文件
    test_content = """#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE>
#!   这是一个演示FMW文件
#!   包含一些测试内容
#! </WORKSPACE>"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.fmw', delete=False) as f:
        f.write(test_content)
        test_file = f.name
    
    print(f"创建测试文件: {os.path.basename(test_file)}")
    
    # 加密演示
    encryption = FMWEncryption()
    encrypted_file = encryption.encrypt_file(test_file)
    print(f"✓ 文件已加密: {os.path.basename(encrypted_file)}")
    
    # 解密演示
    decrypted_file = encryption.decrypt_file(encrypted_file)
    print(f"✓ 文件已解密: {os.path.basename(decrypted_file)}")
    
    # 验证内容
    with open(decrypted_file, 'r') as f:
        decrypted_content = f.read()
    
    if decrypted_content == test_content:
        print("✓ 加密解密验证成功")
    else:
        print("✗ 加密解密验证失败")
    
    # 清理文件
    for file_path in [test_file, encrypted_file, decrypted_file]:
        if os.path.exists(file_path):
            os.unlink(file_path)
    
    # 3. 许可证管理演示
    print("\n3. 许可证管理演示")
    print("-" * 30)
    
    license_manager = LicenseManager()
    print(f"机器ID: {license_manager.machine_id}")
    
    # 生成许可证
    expire_date = datetime.now() + timedelta(days=30)
    license_data = license_manager.generate_license(
        expire_date=expire_date,
        max_uses=100,
        allowed_machines=[license_manager.machine_id]
    )
    
    print(f"✓ 许可证已生成")
    print(f"  许可证ID: {license_data['license_id']}")
    print(f"  过期时间: {expire_date.strftime('%Y-%m-%d')}")
    print(f"  最大使用次数: {license_data['max_uses']}")
    
    # 验证许可证
    valid, message = license_manager.validate_license(license_data)
    print(f"✓ 许可证验证: {message}")
    
    # 4. 模型管理演示
    print("\n4. 模型管理演示")
    print("-" * 30)
    
    from model_manager import ModelManager
    
    # 创建临时目录用于演示
    temp_dir = tempfile.mkdtemp()
    original_models_dir = config.get("models_dir")
    config.set("models_dir", temp_dir)
    
    try:
        manager = ModelManager()
        
        # 创建演示FMW文件
        demo_fmw_content = '''#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE>
#!   演示模型文件
#!   用于展示模型管理功能
#! </WORKSPACE>'''
        
        demo_fmw_path = os.path.join(temp_dir, "demo_model.fmw")
        with open(demo_fmw_path, 'w', encoding='utf-8') as f:
            f.write(demo_fmw_content)
        
        # 导入模型
        success, message, model_id = manager.import_model(
            demo_fmw_path,
            "演示模型",
            "这是一个用于演示的FMW模型",
            "演示分类"
        )
        
        if success:
            print(f"✓ 模型导入成功: {model_id}")
            
            # 获取模型信息
            model_info = manager.get_model_info(model_id)
            print(f"  模型名称: {model_info['name']}")
            print(f"  模型分类: {model_info['category']}")
            print(f"  文件大小: {model_info['file_size']} 字节")
            
            # 搜索模型
            search_results = manager.search_models("演示")
            print(f"✓ 搜索到 {len(search_results)} 个模型")
            
            # 加密模型
            success, message = manager.encrypt_model(model_id)
            if success:
                print("✓ 模型加密成功")
                
                # 解密模型
                success, message, decrypted_path = manager.decrypt_model(model_id)
                if success:
                    print("✓ 模型解密成功")
                else:
                    print(f"✗ 模型解密失败: {message}")
            else:
                print(f"✗ 模型加密失败: {message}")
        else:
            print(f"✗ 模型导入失败: {message}")
    
    finally:
        # 恢复配置并清理
        config.set("models_dir", original_models_dir)
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    # 5. 任务管理演示
    print("\n5. 任务管理演示")
    print("-" * 30)
    
    from fmw_runner import TaskManager
    
    task_manager = TaskManager()
    
    # 创建演示任务
    task_id = task_manager.create_task(
        "demo_model.fmw",
        {"input_file": "demo_input.shp", "output_dir": "demo_output"},
        "/demo/output"
    )
    
    print(f"✓ 任务已创建: {task_id}")
    
    # 更新任务状态
    task_manager.update_task_status(task_id, 'running')
    print("✓ 任务状态已更新为运行中")
    
    # 添加进度信息
    task_manager.add_task_progress(task_id, "开始处理数据...")
    task_manager.add_task_progress(task_id, "正在转换格式...")
    task_manager.add_task_progress(task_id, "处理完成")
    
    # 完成任务
    task_manager.update_task_status(task_id, 'completed', {
        'success': True,
        'execution_time': 15.5,
        'output_files': ['output1.shp', 'output2.shp']
    })
    
    print("✓ 任务已完成")
    
    # 获取任务信息
    task = task_manager.get_task(task_id)
    if task:
        print(f"  任务状态: {task['status']}")
        print(f"  执行时间: {task['execution_time']}秒")
        print(f"  进度记录: {len(task['progress'])}条")
    
    # 6. 客户端生成演示
    print("\n6. 客户端生成演示")
    print("-" * 30)
    
    from client_generator import ClientGenerator
    
    # 创建临时目录
    temp_clients_dir = tempfile.mkdtemp()
    original_clients_dir = config.get("clients_dir")
    config.set("clients_dir", temp_clients_dir)
    
    try:
        generator = ClientGenerator()
        
        # 获取客户端模板
        template = generator.create_client_template()
        print(f"✓ 客户端模板已生成 ({len(template)} 字符)")
        
        # 获取客户端列表
        clients = generator.get_client_list()
        print(f"✓ 当前客户端数量: {len(clients)}")
        
    finally:
        # 恢复配置并清理
        config.set("clients_dir", original_clients_dir)
        shutil.rmtree(temp_clients_dir, ignore_errors=True)
    
    print("\n" + "=" * 60)
    print("演示完成！所有核心功能运行正常。")
    print("=" * 60)

def demo_ui_components():
    """演示UI组件（如果可用）"""
    print("\n7. UI组件演示")
    print("-" * 30)
    
    try:
        import tkinter as tk
        import ttkbootstrap as ttk_bs
        
        print("✓ Tkinter 可用")
        print("✓ ttkbootstrap 可用")
        
        # 创建一个简单的演示窗口
        root = tk.Tk()
        root.title("FME工具演示")
        root.geometry("400x300")
        
        # 添加一些组件
        ttk_bs.Label(root, text="FME模型管理工具", 
                    font=("", 16, "bold")).pack(pady=20)
        
        ttk_bs.Label(root, text="UI组件测试成功！").pack(pady=10)
        
        ttk_bs.Button(root, text="关闭", 
                     command=root.destroy).pack(pady=10)
        
        print("✓ 演示窗口已创建")
        print("  (窗口将在3秒后自动关闭)")
        
        # 3秒后自动关闭
        root.after(3000, root.destroy)
        root.mainloop()
        
        print("✓ UI组件演示完成")
        
    except ImportError as e:
        print(f"✗ UI组件不可用: {e}")
    except Exception as e:
        print(f"✗ UI组件演示失败: {e}")

if __name__ == "__main__":
    try:
        demo_basic_functionality()
        demo_ui_components()
        
        print("\n🎉 FME模型管理工具演示成功完成！")
        print("\n要启动完整的图形界面，请运行:")
        print("  python main.py")
        print("或双击 start.bat 文件")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
