"""
简单的UI测试
"""
import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

print("开始测试...")

try:
    print("1. 测试tkinter...")
    import tkinter as tk
    print("   ✓ tkinter导入成功")
    
    print("2. 测试ttkbootstrap...")
    import ttkbootstrap as ttk_bs
    print("   ✓ ttkbootstrap导入成功")
    
    print("3. 测试config...")
    from config import config
    print("   ✓ config导入成功")
    
    print("4. 创建简单窗口...")
    root = tk.Tk()
    root.title("简单测试")
    root.geometry("300x200")
    
    label = tk.Label(root, text="Hello World!", font=("Arial", 14))
    label.pack(expand=True)
    
    print("   ✓ 简单窗口创建成功")
    
    # 2秒后关闭
    root.after(2000, root.destroy)
    root.mainloop()
    
    print("5. 测试ttkbootstrap主题...")
    style = ttk_bs.Style(theme="cosmo")
    root2 = style.master
    root2.title("主题测试")
    root2.geometry("300x200")
    
    frame = ttk_bs.Frame(root2, padding=20)
    frame.pack(fill="both", expand=True)
    
    label2 = ttk_bs.Label(frame, text="ttkbootstrap测试", font=("Arial", 14))
    label2.pack(expand=True)
    
    print("   ✓ ttkbootstrap主题窗口创建成功")
    
    # 2秒后关闭
    root2.after(2000, root2.destroy)
    root2.mainloop()
    
    print("所有测试通过！")
    
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()
