"""
FMW文件加密解密模块
"""
import os
import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import json
from datetime import datetime, timedelta
import platform
import uuid
import hashlib

class FMWEncryption:
    """FMW文件加密解密类"""
    
    def __init__(self, key_file="encryption.key"):
        self.key_file = key_file
        self.key = self.load_or_generate_key()
        
    def load_or_generate_key(self):
        """加载或生成加密密钥"""
        if os.path.exists(self.key_file):
            try:
                with open(self.key_file, 'rb') as f:
                    return f.read()
            except Exception as e:
                print(f"加载密钥失败: {e}")
                
        # 生成新密钥
        key = Fernet.generate_key()
        try:
            with open(self.key_file, 'wb') as f:
                f.write(key)
            print(f"生成新密钥: {self.key_file}")
        except Exception as e:
            print(f"保存密钥失败: {e}")
            
        return key
    
    def encrypt_file(self, file_path, output_path=None):
        """加密FMW文件"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        if output_path is None:
            output_path = file_path + ".encrypted"
            
        try:
            fernet = Fernet(self.key)
            
            with open(file_path, 'rb') as f:
                file_data = f.read()
                
            encrypted_data = fernet.encrypt(file_data)
            
            with open(output_path, 'wb') as f:
                f.write(encrypted_data)
                
            print(f"文件加密成功: {output_path}")
            return output_path
            
        except Exception as e:
            raise Exception(f"加密失败: {e}")
    
    def decrypt_file(self, encrypted_file_path, output_path=None):
        """解密FMW文件"""
        if not os.path.exists(encrypted_file_path):
            raise FileNotFoundError(f"加密文件不存在: {encrypted_file_path}")
            
        if output_path is None:
            output_path = encrypted_file_path.replace(".encrypted", "")
            
        try:
            fernet = Fernet(self.key)
            
            with open(encrypted_file_path, 'rb') as f:
                encrypted_data = f.read()
                
            decrypted_data = fernet.decrypt(encrypted_data)
            
            with open(output_path, 'wb') as f:
                f.write(decrypted_data)
                
            print(f"文件解密成功: {output_path}")
            return output_path
            
        except Exception as e:
            raise Exception(f"解密失败: {e}")

class LicenseManager:
    """许可证管理类"""

    def __init__(self):
        self.machine_id = self.get_machine_id()

    def get_machine_id(self):
        """获取机器唯一标识"""
        # 获取机器的唯一标识符
        machine_info = f"{platform.node()}-{platform.machine()}-{platform.processor()}"
        return hashlib.md5(machine_info.encode()).hexdigest()

    def generate_license(self, expire_date=None, max_uses=None, allowed_machines=None):
        """生成许可证"""
        license_data = {
            "license_id": str(uuid.uuid4()),
            "created_at": datetime.now().isoformat(),
            "expire_date": expire_date.isoformat() if expire_date else None,
            "max_uses": max_uses,
            "current_uses": 0,
            "allowed_machines": allowed_machines or [self.machine_id],
            "is_active": True
        }

        return license_data

    def generate_model_license(self, model_id, model_name, expire_date=None, max_uses=None, allowed_machines=None):
        """为特定模型生成许可证"""
        license_data = {
            "license_id": str(uuid.uuid4()),
            "model_id": model_id,
            "model_name": model_name,
            "created_at": datetime.now().isoformat(),
            "expire_date": expire_date.isoformat() if expire_date else None,
            "max_uses": max_uses,
            "current_uses": 0,
            "allowed_machines": allowed_machines or [self.machine_id],
            "is_active": True
        }

        return license_data
    
    def save_license(self, license_data, file_path):
        """保存许可证到文件"""
        try:
            # 生成新的加密密钥
            key = Fernet.generate_key()
            fernet = Fernet(key)

            # 加密许可证数据
            license_json = json.dumps(license_data, ensure_ascii=False)
            encrypted_license = fernet.encrypt(license_json.encode())

            # 保存加密的许可证和密钥
            with open(file_path, 'wb') as f:
                f.write(base64.b64encode(key + encrypted_license))

            print(f"许可证保存成功: {file_path}")
            return True

        except Exception as e:
            print(f"保存许可证失败: {e}")
            return False

    def save_multi_model_license(self, model_licenses, file_path):
        """保存多模型许可证文件"""
        try:
            license_lines = []

            for model_id, license_data in model_licenses.items():
                # 为每个模型生成独立的加密许可证
                key = Fernet.generate_key()
                fernet = Fernet(key)

                # 加密许可证数据
                license_json = json.dumps(license_data, ensure_ascii=False)
                encrypted_license = fernet.encrypt(license_json.encode())

                # 组合密钥和加密数据
                combined_data = base64.b64encode(key + encrypted_license).decode()

                # 格式：MODEL_ID:ENCRYPTED_LICENSE_DATA
                license_lines.append(f"{model_id}:{combined_data}")

            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(license_lines))

            print(f"多模型许可证保存成功: {file_path}")
            return True

        except Exception as e:
            print(f"保存多模型许可证失败: {e}")
            return False
    
    def load_license(self, file_path):
        """从文件加载许可证"""
        try:
            with open(file_path, 'rb') as f:
                data = base64.b64decode(f.read())

            # 提取密钥和加密数据
            key = data[:44]  # Fernet密钥长度为44字节
            encrypted_data = data[44:]

            fernet = Fernet(key)
            decrypted_data = fernet.decrypt(encrypted_data)

            license_data = json.loads(decrypted_data.decode())
            return license_data

        except Exception as e:
            print(f"加载许可证失败: {e}")
            return None

    def load_multi_model_license(self, file_path):
        """从文件加载多模型许可证"""
        try:
            model_licenses = {}

            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            for line in lines:
                line = line.strip()
                if not line or ':' not in line:
                    continue

                # 解析格式：MODEL_ID:ENCRYPTED_LICENSE_DATA
                model_id, encrypted_data_str = line.split(':', 1)

                try:
                    # 解码Base64数据
                    encrypted_data = base64.b64decode(encrypted_data_str.encode())

                    # 分离密钥和加密的许可证数据
                    key = encrypted_data[:44]  # Fernet密钥长度为44字节
                    encrypted_license = encrypted_data[44:]

                    # 解密许可证数据
                    fernet = Fernet(key)
                    license_json = fernet.decrypt(encrypted_license).decode()
                    license_data = json.loads(license_json)

                    model_licenses[model_id] = license_data

                except Exception as e:
                    print(f"解析模型 {model_id} 的许可证失败: {e}")
                    continue

            print(f"多模型许可证加载成功: {file_path}, 共加载 {len(model_licenses)} 个模型许可证")
            return model_licenses

        except Exception as e:
            print(f"加载多模型许可证失败: {e}")
            return None
    
    def validate_license(self, license_data):
        """验证许可证"""
        if not license_data or not license_data.get("is_active"):
            return False, "许可证无效或已禁用"

        # 检查过期时间
        if license_data.get("expire_date"):
            expire_date = datetime.fromisoformat(license_data["expire_date"])
            if datetime.now() > expire_date:
                return False, "许可证已过期"

        # 检查使用次数
        max_uses = license_data.get("max_uses")
        if max_uses and license_data.get("current_uses", 0) >= max_uses:
            return False, "许可证使用次数已达上限"

        # 检查机器码
        allowed_machines = license_data.get("allowed_machines", [])
        if allowed_machines and self.machine_id not in allowed_machines:
            return False, "机器码不匹配"

        return True, "许可证有效"

    def validate_model_license(self, model_id, model_licenses):
        """验证特定模型的许可证"""
        if not model_licenses or model_id not in model_licenses:
            return False, f"模型 {model_id} 没有有效许可证"

        license_data = model_licenses[model_id]
        return self.validate_license(license_data)

    def get_licensed_models(self, license_file_path):
        """获取许可证文件中的所有已授权模型"""
        model_licenses = self.load_multi_model_license(license_file_path)
        if not model_licenses:
            return []

        licensed_models = []
        for model_id, license_data in model_licenses.items():
            valid, message = self.validate_license(license_data)
            if valid:
                licensed_models.append({
                    'model_id': model_id,
                    'model_name': license_data.get('model_name', model_id),
                    'license_data': license_data
                })

        return licensed_models
    
    def use_license(self, license_data, file_path):
        """使用许可证（增加使用次数）"""
        license_data["current_uses"] = license_data.get("current_uses", 0) + 1
        return self.save_license(license_data, file_path)
