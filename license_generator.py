"""
许可证生成器（注册机）
用于生成和管理软件许可证
"""
import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import hashlib
import platform
import uuid
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
import base64

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from encryption import LicenseManager

class LicenseGenerator:
    """许可证生成器主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("FME工具许可证生成器")
        self.root.geometry("800x600")
        
        # 居中显示
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (400)
        y = (self.root.winfo_screenheight() // 2) - (300)
        self.root.geometry(f"800x600+{x}+{y}")
        
        self.license_manager = LicenseManager()
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="FME工具许可证生成器", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 创建选项卡
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 许可证生成选项卡
        self.create_generate_tab(notebook)
        
        # 许可证验证选项卡
        self.create_validate_tab(notebook)
        
        # 机器码管理选项卡
        self.create_machine_tab(notebook)
        
        # 批量生成选项卡
        self.create_batch_tab(notebook)
    
    def create_generate_tab(self, notebook):
        """创建许可证生成选项卡"""
        generate_frame = ttk.Frame(notebook)
        notebook.add(generate_frame, text="生成许可证")
        
        # 滚动框架
        canvas = tk.Canvas(generate_frame)
        scrollbar = ttk.Scrollbar(generate_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 基本信息
        basic_frame = ttk.LabelFrame(scrollable_frame, text="基本信息", padding=10)
        basic_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(basic_frame, text="客户端名称:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.client_name_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.client_name_var, width=40).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        ttk.Label(basic_frame, text="许可证描述:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.license_desc_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.license_desc_var, width=40).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        basic_frame.columnconfigure(1, weight=1)
        
        # 时间限制
        time_frame = ttk.LabelFrame(scrollable_frame, text="时间限制", padding=10)
        time_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.enable_expire_var = tk.BooleanVar()
        ttk.Checkbutton(time_frame, text="启用过期时间", variable=self.enable_expire_var,
                       command=self.toggle_expire).pack(anchor=tk.W, pady=5)
        
        expire_input_frame = ttk.Frame(time_frame)
        expire_input_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(expire_input_frame, text="过期日期:").pack(side=tk.LEFT)
        self.expire_date_var = tk.StringVar(value=(datetime.now() + timedelta(days=365)).strftime("%Y-%m-%d"))
        self.expire_date_entry = ttk.Entry(expire_input_frame, textvariable=self.expire_date_var, width=15)
        self.expire_date_entry.pack(side=tk.LEFT, padx=(10, 0))
        self.expire_date_entry.config(state=tk.DISABLED)
        
        ttk.Label(expire_input_frame, text="或天数:").pack(side=tk.LEFT, padx=(20, 0))
        self.expire_days_var = tk.StringVar(value="365")
        self.expire_days_entry = ttk.Entry(expire_input_frame, textvariable=self.expire_days_var, width=10)
        self.expire_days_entry.pack(side=tk.LEFT, padx=(5, 0))
        self.expire_days_entry.config(state=tk.DISABLED)
        
        def update_expire_date():
            try:
                days = int(self.expire_days_var.get())
                new_date = datetime.now() + timedelta(days=days)
                self.expire_date_var.set(new_date.strftime("%Y-%m-%d"))
            except ValueError:
                pass
        
        self.expire_days_entry.bind('<KeyRelease>', lambda e: update_expire_date())
        
        # 使用次数限制
        usage_frame = ttk.LabelFrame(scrollable_frame, text="使用次数限制", padding=10)
        usage_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.enable_usage_var = tk.BooleanVar()
        ttk.Checkbutton(usage_frame, text="启用使用次数限制", variable=self.enable_usage_var,
                       command=self.toggle_usage).pack(anchor=tk.W, pady=5)
        
        usage_input_frame = ttk.Frame(usage_frame)
        usage_input_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(usage_input_frame, text="最大使用次数:").pack(side=tk.LEFT)
        self.max_uses_var = tk.StringVar(value="1000")
        self.max_uses_entry = ttk.Entry(usage_input_frame, textvariable=self.max_uses_var, width=15)
        self.max_uses_entry.pack(side=tk.LEFT, padx=(10, 0))
        self.max_uses_entry.config(state=tk.DISABLED)
        
        # 机器码限制
        machine_frame = ttk.LabelFrame(scrollable_frame, text="机器码限制", padding=10)
        machine_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.enable_machine_var = tk.BooleanVar()
        ttk.Checkbutton(machine_frame, text="启用机器码限制", variable=self.enable_machine_var,
                       command=self.toggle_machine).pack(anchor=tk.W, pady=5)
        
        machine_input_frame = ttk.Frame(machine_frame)
        machine_input_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(machine_input_frame, text="当前机器码:").pack(anchor=tk.W)
        current_machine_label = ttk.Label(machine_input_frame, text=self.license_manager.machine_id,
                                         font=("Consolas", 9), foreground="blue")
        current_machine_label.pack(anchor=tk.W, pady=(0, 5))
        
        ttk.Label(machine_input_frame, text="允许的机器码 (每行一个):").pack(anchor=tk.W)
        self.machine_text = tk.Text(machine_input_frame, height=4, width=60)
        self.machine_text.pack(fill=tk.X, pady=(5, 0))
        self.machine_text.insert(1.0, self.license_manager.machine_id)
        self.machine_text.config(state=tk.DISABLED)
        
        # 功能限制
        feature_frame = ttk.LabelFrame(scrollable_frame, text="功能限制", padding=10)
        feature_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.allow_encrypt_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(feature_frame, text="允许模型加密", variable=self.allow_encrypt_var).pack(anchor=tk.W)
        
        self.allow_distribute_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(feature_frame, text="允许客户端分发", variable=self.allow_distribute_var).pack(anchor=tk.W)
        
        self.allow_batch_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(feature_frame, text="允许批量处理", variable=self.allow_batch_var).pack(anchor=tk.W)
        
        # 生成按钮
        button_frame = ttk.Frame(scrollable_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=20)
        
        ttk.Button(button_frame, text="生成许可证", command=self.generate_license).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="保存到文件", command=self.save_license).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="重置", command=self.reset_form).pack(side=tk.LEFT, padx=5)
        
        # 结果显示
        result_frame = ttk.LabelFrame(scrollable_frame, text="生成结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.result_text = tk.Text(result_frame, height=8, wrap=tk.WORD)
        result_scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=result_scrollbar.set)
        
        self.result_text.pack(side="left", fill="both", expand=True)
        result_scrollbar.pack(side="right", fill="y")
        
        # 布局滚动框架
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        self.current_license = None
    
    def create_validate_tab(self, notebook):
        """创建许可证验证选项卡"""
        validate_frame = ttk.Frame(notebook)
        notebook.add(validate_frame, text="验证许可证")
        
        main_frame = ttk.Frame(validate_frame, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(main_frame, text="许可证验证", font=("Arial", 14, "bold")).pack(pady=(0, 20))
        
        # 文件选择
        file_frame = ttk.Frame(main_frame)
        file_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(file_frame, text="许可证文件:").pack(side=tk.LEFT)
        self.license_file_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.license_file_var).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))
        ttk.Button(file_frame, text="浏览", command=self.browse_license_file).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 验证按钮
        ttk.Button(main_frame, text="验证许可证", command=self.validate_license).pack(pady=10)
        
        # 验证结果
        self.validate_result_text = tk.Text(main_frame, height=15, wrap=tk.WORD)
        validate_scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.validate_result_text.yview)
        self.validate_result_text.configure(yscrollcommand=validate_scrollbar.set)
        
        self.validate_result_text.pack(side="left", fill="both", expand=True)
        validate_scrollbar.pack(side="right", fill="y")
    
    def create_machine_tab(self, notebook):
        """创建机器码管理选项卡"""
        machine_frame = ttk.Frame(notebook)
        notebook.add(machine_frame, text="机器码管理")
        
        main_frame = ttk.Frame(machine_frame, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(main_frame, text="机器码管理", font=("Arial", 14, "bold")).pack(pady=(0, 20))
        
        # 当前机器信息
        info_frame = ttk.LabelFrame(main_frame, text="当前机器信息", padding=10)
        info_frame.pack(fill=tk.X, pady=10)
        
        machine_info = f"""机器名: {platform.node()}
处理器: {platform.processor()}
系统: {platform.system()} {platform.release()}
架构: {platform.machine()}
机器码: {self.license_manager.machine_id}"""
        
        ttk.Label(info_frame, text=machine_info, font=("Consolas", 9)).pack(anchor=tk.W)
        
        # 机器码生成
        generate_frame = ttk.LabelFrame(main_frame, text="生成机器码", padding=10)
        generate_frame.pack(fill=tk.X, pady=10)
        
        input_frame = ttk.Frame(generate_frame)
        input_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(input_frame, text="机器信息:").pack(side=tk.LEFT)
        self.machine_info_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.machine_info_var).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))
        ttk.Button(input_frame, text="生成机器码", command=self.generate_machine_code).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 结果显示
        self.machine_result_text = tk.Text(generate_frame, height=5, wrap=tk.WORD)
        self.machine_result_text.pack(fill=tk.X, pady=(10, 0))
    
    def create_batch_tab(self, notebook):
        """创建批量生成选项卡"""
        batch_frame = ttk.Frame(notebook)
        notebook.add(batch_frame, text="批量生成")
        
        main_frame = ttk.Frame(batch_frame, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(main_frame, text="批量许可证生成", font=("Arial", 14, "bold")).pack(pady=(0, 20))
        
        # 批量设置
        settings_frame = ttk.LabelFrame(main_frame, text="批量设置", padding=10)
        settings_frame.pack(fill=tk.X, pady=10)
        
        count_frame = ttk.Frame(settings_frame)
        count_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(count_frame, text="生成数量:").pack(side=tk.LEFT)
        self.batch_count_var = tk.StringVar(value="10")
        ttk.Entry(count_frame, textvariable=self.batch_count_var, width=10).pack(side=tk.LEFT, padx=(10, 0))
        
        prefix_frame = ttk.Frame(settings_frame)
        prefix_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(prefix_frame, text="文件名前缀:").pack(side=tk.LEFT)
        self.batch_prefix_var = tk.StringVar(value="license_")
        ttk.Entry(prefix_frame, textvariable=self.batch_prefix_var, width=20).pack(side=tk.LEFT, padx=(10, 0))
        
        # 批量生成按钮
        ttk.Button(settings_frame, text="批量生成", command=self.batch_generate).pack(pady=10)
        
        # 进度显示
        self.batch_progress = ttk.Progressbar(main_frame, mode='determinate')
        self.batch_progress.pack(fill=tk.X, pady=10)
        
        # 批量结果
        self.batch_result_text = tk.Text(main_frame, height=10, wrap=tk.WORD)
        batch_scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.batch_result_text.yview)
        self.batch_result_text.configure(yscrollcommand=batch_scrollbar.set)
        
        self.batch_result_text.pack(side="left", fill="both", expand=True)
        batch_scrollbar.pack(side="right", fill="y")
    
    def toggle_expire(self):
        """切换过期时间设置"""
        if self.enable_expire_var.get():
            self.expire_date_entry.config(state=tk.NORMAL)
            self.expire_days_entry.config(state=tk.NORMAL)
        else:
            self.expire_date_entry.config(state=tk.DISABLED)
            self.expire_days_entry.config(state=tk.DISABLED)
    
    def toggle_usage(self):
        """切换使用次数设置"""
        if self.enable_usage_var.get():
            self.max_uses_entry.config(state=tk.NORMAL)
        else:
            self.max_uses_entry.config(state=tk.DISABLED)
    
    def toggle_machine(self):
        """切换机器码设置"""
        if self.enable_machine_var.get():
            self.machine_text.config(state=tk.NORMAL)
        else:
            self.machine_text.config(state=tk.DISABLED)
    
    def generate_license(self):
        """生成许可证"""
        try:
            # 获取设置
            expire_date = None
            if self.enable_expire_var.get():
                expire_date_str = self.expire_date_var.get()
                expire_date = datetime.strptime(expire_date_str, "%Y-%m-%d")
            
            max_uses = None
            if self.enable_usage_var.get():
                max_uses = int(self.max_uses_var.get())
            
            allowed_machines = None
            if self.enable_machine_var.get():
                machine_codes = self.machine_text.get(1.0, tk.END).strip().split('\n')
                allowed_machines = [code.strip() for code in machine_codes if code.strip()]
            
            # 生成许可证
            license_data = self.license_manager.generate_license(
                expire_date=expire_date,
                max_uses=max_uses,
                allowed_machines=allowed_machines
            )
            
            # 添加额外信息
            license_data.update({
                "client_name": self.client_name_var.get(),
                "description": self.license_desc_var.get(),
                "features": {
                    "allow_encrypt": self.allow_encrypt_var.get(),
                    "allow_distribute": self.allow_distribute_var.get(),
                    "allow_batch": self.allow_batch_var.get()
                }
            })
            
            self.current_license = license_data
            
            # 显示结果
            result_text = f"""许可证生成成功！

许可证ID: {license_data['license_id']}
客户端名称: {license_data.get('client_name', '未设置')}
描述: {license_data.get('description', '未设置')}
创建时间: {license_data['created_at']}
过期时间: {license_data.get('expire_date', '无限制')}
最大使用次数: {license_data.get('max_uses', '无限制')}
当前使用次数: {license_data['current_uses']}
允许的机器码: {len(license_data.get('allowed_machines', []))}个

功能权限:
- 模型加密: {'允许' if license_data['features']['allow_encrypt'] else '禁止'}
- 客户端分发: {'允许' if license_data['features']['allow_distribute'] else '禁止'}
- 批量处理: {'允许' if license_data['features']['allow_batch'] else '禁止'}

许可证数据 (JSON格式):
{json.dumps(license_data, ensure_ascii=False, indent=2)}"""
            
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(1.0, result_text)
            
        except Exception as e:
            messagebox.showerror("错误", f"生成许可证失败: {e}")
    
    def save_license(self):
        """保存许可证到文件"""
        if not self.current_license:
            messagebox.showwarning("警告", "请先生成许可证")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="保存许可证",
            defaultextension=".lic",
            filetypes=[("许可证文件", "*.lic"), ("所有文件", "*.*")],
            initialvalue=f"{self.current_license.get('client_name', 'license')}.lic"
        )
        
        if file_path:
            try:
                success = self.license_manager.save_license(self.current_license, file_path)
                if success:
                    messagebox.showinfo("成功", f"许可证已保存到: {file_path}")
                else:
                    messagebox.showerror("错误", "保存许可证失败")
            except Exception as e:
                messagebox.showerror("错误", f"保存许可证失败: {e}")
    
    def reset_form(self):
        """重置表单"""
        self.client_name_var.set("")
        self.license_desc_var.set("")
        self.enable_expire_var.set(False)
        self.enable_usage_var.set(False)
        self.enable_machine_var.set(False)
        self.allow_encrypt_var.set(True)
        self.allow_distribute_var.set(True)
        self.allow_batch_var.set(True)
        
        self.toggle_expire()
        self.toggle_usage()
        self.toggle_machine()
        
        self.result_text.delete(1.0, tk.END)
        self.current_license = None
    
    def browse_license_file(self):
        """浏览许可证文件"""
        file_path = filedialog.askopenfilename(
            title="选择许可证文件",
            filetypes=[("许可证文件", "*.lic"), ("所有文件", "*.*")]
        )
        if file_path:
            self.license_file_var.set(file_path)
    
    def validate_license(self):
        """验证许可证"""
        file_path = self.license_file_var.get()
        if not file_path:
            messagebox.showwarning("警告", "请选择许可证文件")
            return
        
        try:
            # 加载许可证
            license_data = self.license_manager.load_license(file_path)
            if not license_data:
                self.validate_result_text.delete(1.0, tk.END)
                self.validate_result_text.insert(1.0, "许可证文件无效或损坏")
                return
            
            # 验证许可证
            valid, message = self.license_manager.validate_license(license_data)
            
            # 显示验证结果
            result_text = f"""许可证验证结果: {'有效' if valid else '无效'}
验证消息: {message}

许可证详情:
许可证ID: {license_data.get('license_id', '未知')}
客户端名称: {license_data.get('client_name', '未设置')}
描述: {license_data.get('description', '未设置')}
创建时间: {license_data.get('created_at', '未知')}
过期时间: {license_data.get('expire_date', '无限制')}
最大使用次数: {license_data.get('max_uses', '无限制')}
当前使用次数: {license_data.get('current_uses', 0)}
允许的机器码数量: {len(license_data.get('allowed_machines', []))}

当前机器码: {self.license_manager.machine_id}
机器码匹配: {'是' if self.license_manager.machine_id in license_data.get('allowed_machines', []) else '否'}

功能权限:
{json.dumps(license_data.get('features', {}), ensure_ascii=False, indent=2)}"""
            
            self.validate_result_text.delete(1.0, tk.END)
            self.validate_result_text.insert(1.0, result_text)
            
        except Exception as e:
            self.validate_result_text.delete(1.0, tk.END)
            self.validate_result_text.insert(1.0, f"验证许可证时出错: {e}")
    
    def generate_machine_code(self):
        """生成机器码"""
        machine_info = self.machine_info_var.get().strip()
        if not machine_info:
            messagebox.showwarning("警告", "请输入机器信息")
            return
        
        machine_code = hashlib.md5(machine_info.encode()).hexdigest()
        
        result_text = f"""输入的机器信息: {machine_info}
生成的机器码: {machine_code}

使用说明:
1. 将此机器码添加到许可证的允许机器码列表中
2. 客户端运行时会验证机器码是否匹配
3. 机器码基于机器的硬件信息生成，具有唯一性"""
        
        self.machine_result_text.delete(1.0, tk.END)
        self.machine_result_text.insert(1.0, result_text)
    
    def batch_generate(self):
        """批量生成许可证"""
        try:
            count = int(self.batch_count_var.get())
            prefix = self.batch_prefix_var.get()
            
            if count <= 0 or count > 1000:
                messagebox.showwarning("警告", "生成数量必须在1-1000之间")
                return
            
            # 选择保存目录
            save_dir = filedialog.askdirectory(title="选择保存目录")
            if not save_dir:
                return
            
            self.batch_progress['maximum'] = count
            self.batch_progress['value'] = 0
            
            self.batch_result_text.delete(1.0, tk.END)
            self.batch_result_text.insert(tk.END, f"开始批量生成 {count} 个许可证...\n\n")
            
            success_count = 0
            
            for i in range(count):
                try:
                    # 生成许可证
                    license_data = self.license_manager.generate_license(
                        expire_date=datetime.now() + timedelta(days=365),
                        max_uses=1000
                    )
                    
                    license_data.update({
                        "client_name": f"{prefix}{i+1:04d}",
                        "description": f"批量生成的许可证 #{i+1}",
                        "features": {
                            "allow_encrypt": True,
                            "allow_distribute": True,
                            "allow_batch": True
                        }
                    })
                    
                    # 保存到文件
                    file_path = os.path.join(save_dir, f"{prefix}{i+1:04d}.lic")
                    if self.license_manager.save_license(license_data, file_path):
                        success_count += 1
                        self.batch_result_text.insert(tk.END, f"✓ {file_path}\n")
                    else:
                        self.batch_result_text.insert(tk.END, f"✗ 保存失败: {file_path}\n")
                    
                    # 更新进度
                    self.batch_progress['value'] = i + 1
                    self.root.update_idletasks()
                    
                except Exception as e:
                    self.batch_result_text.insert(tk.END, f"✗ 生成失败 #{i+1}: {e}\n")
            
            self.batch_result_text.insert(tk.END, f"\n批量生成完成！成功: {success_count}/{count}")
            messagebox.showinfo("完成", f"批量生成完成！\n成功生成: {success_count}/{count} 个许可证")
            
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数量")
        except Exception as e:
            messagebox.showerror("错误", f"批量生成失败: {e}")
    
    def run(self):
        """运行许可证生成器"""
        self.root.mainloop()

if __name__ == "__main__":
    try:
        print("启动许可证生成器...")
        generator = LicenseGenerator()
        generator.run()
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
