"""
最终的窗口居中测试
"""
import sys
import os
import tkinter as tk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_all_dialogs():
    """测试所有对话框的居中效果"""
    # 创建主窗口
    style = ttk_bs.Style(theme="cosmo")
    root = style.master
    root.title("窗口居中测试")
    root.geometry("800x600")
    
    # 主窗口居中
    from dialogs import center_window
    center_window(root)
    
    def test_settings():
        """测试设置对话框"""
        try:
            from dialogs import SettingsDialog
            dialog = SettingsDialog(root)
            print("✓ 设置对话框居中测试完成")
        except Exception as e:
            print(f"✗ 设置对话框测试失败: {e}")
    
    def test_client_create():
        """测试客户端创建对话框"""
        try:
            from dialogs import ClientCreateDialog
            dialog = ClientCreateDialog(root)
            print("✓ 客户端创建对话框居中测试完成")
        except Exception as e:
            print(f"✗ 客户端创建对话框测试失败: {e}")
    
    def test_license_generator():
        """测试许可证生成器"""
        try:
            from multi_model_license_generator import MultiModelLicenseGenerator
            generator = MultiModelLicenseGenerator(root)
            print("✓ 许可证生成器居中测试完成")
        except Exception as e:
            print(f"✗ 许可证生成器测试失败: {e}")
    
    def test_simple_dialog():
        """测试简单对话框"""
        dialog = tk.Toplevel(root)
        dialog.title("简单测试对话框")
        dialog.transient(root)
        dialog.grab_set()
        
        frame = ttk_bs.Frame(dialog, padding=20)
        frame.pack(fill=BOTH, expand=True)
        
        label = ttk_bs.Label(frame, text="这是一个简单的测试对话框\n应该在主窗口中央显示", 
                            font=("Arial", 12), justify=CENTER)
        label.pack(pady=20)
        
        button = ttk_bs.Button(frame, text="关闭", command=dialog.destroy)
        button.pack(pady=10)
        
        # 使用统一的居中函数
        center_window(dialog, root, 400, 200)
        print("✓ 简单对话框居中测试完成")
    
    # 创建测试按钮
    main_frame = ttk_bs.Frame(root, padding=20)
    main_frame.pack(fill=BOTH, expand=True)
    
    title_label = ttk_bs.Label(main_frame, text="窗口居中效果测试", 
                              font=("Arial", 16, "bold"))
    title_label.pack(pady=20)
    
    info_label = ttk_bs.Label(main_frame, 
                             text="点击下面的按钮测试各种对话框的居中效果\n"
                                  "每个对话框都应该在主窗口的中央显示", 
                             font=("Arial", 10), justify=CENTER)
    info_label.pack(pady=10)
    
    button_frame = ttk_bs.Frame(main_frame)
    button_frame.pack(pady=20)
    
    ttk_bs.Button(button_frame, text="测试简单对话框", 
                 command=test_simple_dialog, bootstyle=PRIMARY).pack(pady=5, fill=X)
    
    ttk_bs.Button(button_frame, text="测试设置对话框", 
                 command=test_settings, bootstyle=SECONDARY).pack(pady=5, fill=X)
    
    ttk_bs.Button(button_frame, text="测试客户端创建对话框", 
                 command=test_client_create, bootstyle=SUCCESS).pack(pady=5, fill=X)
    
    ttk_bs.Button(button_frame, text="测试许可证生成器", 
                 command=test_license_generator, bootstyle=INFO).pack(pady=5, fill=X)
    
    # 状态标签
    status_label = ttk_bs.Label(main_frame, 
                               text="状态：准备就绪\n"
                                    "如果对话框没有在主窗口中央显示，说明居中功能有问题", 
                               font=("Arial", 9), foreground="gray")
    status_label.pack(pady=20)
    
    print("窗口居中测试程序已启动")
    print("主窗口应该在屏幕中央显示")
    print("点击按钮测试各种对话框的居中效果")
    
    root.mainloop()

if __name__ == "__main__":
    test_all_dialogs()
