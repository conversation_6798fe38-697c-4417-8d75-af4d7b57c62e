"""
简化的界面演示程序
确保能够看到图形界面
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

def create_demo_window():
    """创建演示窗口"""
    root = tk.Tk()
    root.title("FME模型管理工具 - 界面演示")
    root.geometry("1000x700")
    
    # 设置窗口居中
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (1000 // 2)
    y = (root.winfo_screenheight() // 2) - (700 // 2)
    root.geometry(f"1000x700+{x}+{y}")
    
    # 创建主框架
    main_frame = ttk.Frame(root, padding="10")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="FME模型管理工具", 
                           font=("Arial", 20, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 功能说明
    info_text = """
🎉 恭喜！图形界面已成功创建！

✨ 主要界面特性：
• 现代化的图形用户界面
• 左右分栏布局设计
• 工具栏和菜单栏
• 多选项卡界面
• 实时搜索和筛选
• 拖拽操作支持

🔧 核心功能模块：
• FMW模型导入和管理
• 参数自动解析和配置
• 模型加密和解密
• 任务运行和监控
• 分发客户端生成

🎨 界面组件：
• 模型列表（表格视图）
• 参数设置表单
• 运行日志显示
• 进度条和状态栏
• 各种对话框

📱 支持的操作：
• 点击选择模型
• 右键菜单操作
• 拖拽文件导入
• 实时搜索筛选
• 多主题切换
"""
    
    info_label = ttk.Label(main_frame, text=info_text, 
                          font=("Arial", 11), justify=tk.LEFT)
    info_label.pack(pady=10, padx=20)
    
    # 创建按钮框架
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(pady=20)
    
    # 演示按钮
    def show_main_interface():
        """显示主界面"""
        try:
            # 尝试导入并启动主程序
            sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
            from main_window import MainWindow
            
            root.destroy()  # 关闭演示窗口
            
            # 启动主程序
            app = MainWindow()
            app.run()
            
        except ImportError as e:
            messagebox.showerror("错误", f"无法启动主程序：{e}\n\n请确保所有依赖已安装：\npip install -r requirements.txt")
        except Exception as e:
            messagebox.showerror("错误", f"启动主程序时出错：{e}")
    
    def show_features():
        """显示功能特性"""
        features_window = tk.Toplevel(root)
        features_window.title("功能特性展示")
        features_window.geometry("600x500")
        features_window.transient(root)
        features_window.grab_set()
        
        # 居中显示
        features_window.update_idletasks()
        x = root.winfo_x() + 200
        y = root.winfo_y() + 100
        features_window.geometry(f"600x500+{x}+{y}")
        
        # 创建选项卡
        notebook = ttk.Notebook(features_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 模型管理选项卡
        model_frame = ttk.Frame(notebook)
        notebook.add(model_frame, text="模型管理")
        
        model_text = """
📁 模型管理功能：

• 导入FMW文件到模型库
• 自动解析模型参数
• 模型分类和标签管理
• 搜索和筛选功能
• 模型信息查看
• 使用统计和历史记录

🔒 安全功能：
• AES加密保护模型文件
• 密钥自动管理
• 运行时透明解密
• 防止模型泄露

📊 统计功能：
• 模型使用次数统计
• 最后使用时间记录
• 运行成功率统计
• 性能分析报告
"""
        
        ttk.Label(model_frame, text=model_text, font=("Arial", 10), 
                 justify=tk.LEFT).pack(padx=20, pady=20)
        
        # 运行管理选项卡
        run_frame = ttk.Frame(notebook)
        notebook.add(run_frame, text="运行管理")
        
        run_text = """
🚀 运行管理功能：

• 图形化参数配置界面
• 支持多种参数类型：
  - 文本输入
  - 文件/文件夹选择
  - 下拉选择
  - 日期时间选择
  - 数值输入
  - 布尔选择

⚡ 任务执行：
• 异步任务执行
• 实时进度显示
• 详细日志记录
• 任务队列管理
• 错误处理和重试

📈 监控功能：
• 实时状态监控
• 执行时间统计
• 资源使用监控
• 任务历史查看
"""
        
        ttk.Label(run_frame, text=run_text, font=("Arial", 10), 
                 justify=tk.LEFT).pack(padx=20, pady=20)
        
        # 分发功能选项卡
        dist_frame = ttk.Frame(notebook)
        notebook.add(dist_frame, text="分发功能")
        
        dist_text = """
📦 分发客户端功能：

• 一键生成独立客户端
• 自动打包相关模型
• 生成许可证文件
• 创建安装程序

🔐 许可证管理：
• 时间限制（过期日期）
• 使用次数限制
• 机器码绑定
• 功能权限控制

🎯 客户端特性：
• 独立运行，无需安装主程序
• 简化的用户界面
• 自动许可证验证
• 使用统计上报
• 自动更新支持
"""
        
        ttk.Label(dist_frame, text=dist_text, font=("Arial", 10), 
                 justify=tk.LEFT).pack(padx=20, pady=20)
    
    def show_ui_demo():
        """显示UI组件演示"""
        ui_window = tk.Toplevel(root)
        ui_window.title("UI组件演示")
        ui_window.geometry("800x600")
        ui_window.transient(root)
        ui_window.grab_set()
        
        # 居中显示
        ui_window.update_idletasks()
        x = root.winfo_x() + 100
        y = root.winfo_y() + 50
        ui_window.geometry(f"800x600+{x}+{y}")
        
        # 创建演示界面
        demo_frame = ttk.Frame(ui_window, padding="20")
        demo_frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(demo_frame, text="UI组件演示", 
                 font=("Arial", 16, "bold")).pack(pady=(0, 20))
        
        # 创建左右分栏
        paned = ttk.PanedWindow(demo_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)
        
        # 左侧面板
        left_frame = ttk.LabelFrame(paned, text="模型列表", padding="10")
        paned.add(left_frame, weight=1)
        
        # 搜索框
        search_frame = ttk.Frame(left_frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT)
        search_entry = ttk.Entry(search_frame)
        search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        
        # 模型列表
        columns = ("name", "category", "size")
        tree = ttk.Treeview(left_frame, columns=columns, show="headings", height=10)
        tree.heading("name", text="模型名称")
        tree.heading("category", text="分类")
        tree.heading("size", text="大小")
        
        # 添加示例数据
        tree.insert("", "end", values=("CAD转换模型", "数据转换", "2.1MB"))
        tree.insert("", "end", values=("数据质检工具", "质量检查", "1.8MB"))
        tree.insert("", "end", values=("坐标转换", "数据转换", "3.2MB"))
        tree.insert("", "end", values=("格式转换", "数据转换", "2.5MB"))
        
        tree.pack(fill=tk.BOTH, expand=True)
        
        # 右侧面板
        right_frame = ttk.LabelFrame(paned, text="模型详情", padding="10")
        paned.add(right_frame, weight=2)
        
        # 选项卡
        notebook = ttk.Notebook(right_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 信息选项卡
        info_tab = ttk.Frame(notebook)
        notebook.add(info_tab, text="模型信息")
        
        info_text_widget = tk.Text(info_tab, height=15, wrap=tk.WORD)
        info_text_widget.pack(fill=tk.BOTH, expand=True)
        info_text_widget.insert("1.0", """模型名称: CAD转换模型
模型ID: cad_converter_001
分类: 数据转换
描述: 用于CAD数据格式转换的FMW模型
文件大小: 2.1 MB
创建时间: 2024-08-12 10:30:15
更新时间: 2024-08-12 14:25:30
加密状态: 未加密
使用次数: 15
最后使用: 2024-08-12 14:20:10

参数信息:
- 输入文件: 文件选择参数
- 输出目录: 文件夹选择参数
- 坐标系统: 下拉选择参数
- 精度设置: 数值输入参数
- 启用日志: 布尔选择参数""")
        info_text_widget.config(state=tk.DISABLED)
        
        # 参数选项卡
        param_tab = ttk.Frame(notebook)
        notebook.add(param_tab, text="参数设置")
        
        param_frame = ttk.Frame(param_tab, padding="10")
        param_frame.pack(fill=tk.BOTH, expand=True)
        
        # 参数示例
        ttk.Label(param_frame, text="输入文件:").grid(row=0, column=0, sticky=tk.W, pady=5)
        file_frame = ttk.Frame(param_frame)
        file_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        ttk.Entry(file_frame).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(file_frame, text="浏览").pack(side=tk.RIGHT, padx=(5, 0))
        
        ttk.Label(param_frame, text="输出目录:").grid(row=1, column=0, sticky=tk.W, pady=5)
        dir_frame = ttk.Frame(param_frame)
        dir_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        ttk.Entry(dir_frame).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(dir_frame, text="浏览").pack(side=tk.RIGHT, padx=(5, 0))
        
        ttk.Label(param_frame, text="坐标系统:").grid(row=2, column=0, sticky=tk.W, pady=5)
        coord_combo = ttk.Combobox(param_frame, values=["EPSG:4326", "EPSG:3857", "EPSG:2154"])
        coord_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        coord_combo.set("EPSG:4326")
        
        ttk.Label(param_frame, text="精度设置:").grid(row=3, column=0, sticky=tk.W, pady=5)
        precision_var = tk.StringVar(value="标准")
        precision_frame = ttk.Frame(param_frame)
        precision_frame.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        ttk.Radiobutton(precision_frame, text="高精度", variable=precision_var, value="高精度").pack(side=tk.LEFT)
        ttk.Radiobutton(precision_frame, text="标准", variable=precision_var, value="标准").pack(side=tk.LEFT, padx=(10, 0))
        ttk.Radiobutton(precision_frame, text="快速", variable=precision_var, value="快速").pack(side=tk.LEFT, padx=(10, 0))
        
        enable_log_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(param_frame, text="启用详细日志", variable=enable_log_var).grid(row=4, column=1, sticky=tk.W, pady=10, padx=(10, 0))
        
        # 运行按钮
        button_frame = ttk.Frame(param_frame)
        button_frame.grid(row=5, column=1, pady=20, padx=(10, 0))
        ttk.Button(button_frame, text="运行模型").pack(side=tk.LEFT)
        ttk.Button(button_frame, text="重置参数").pack(side=tk.LEFT, padx=(10, 0))
        
        param_frame.columnconfigure(1, weight=1)
        
        # 日志选项卡
        log_tab = ttk.Frame(notebook)
        notebook.add(log_tab, text="运行日志")
        
        log_text = tk.Text(log_tab, height=15, wrap=tk.WORD)
        log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        log_text.insert("1.0", """[2024-08-12 14:30:15] 开始运行模型: CAD转换模型
[2024-08-12 14:30:16] 验证输入参数...
[2024-08-12 14:30:16] 参数验证通过
[2024-08-12 14:30:17] 初始化FME引擎...
[2024-08-12 14:30:18] 读取输入文件: input.dwg
[2024-08-12 14:30:20] 开始数据转换...
[2024-08-12 14:30:25] 处理几何数据...
[2024-08-12 14:30:30] 转换坐标系统...
[2024-08-12 14:30:35] 写入输出文件...
[2024-08-12 14:30:38] 模型运行完成
[2024-08-12 14:30:38] 总耗时: 23.5秒
[2024-08-12 14:30:38] 处理记录数: 1,250
[2024-08-12 14:30:38] 输出文件: output.shp""")
        log_text.config(state=tk.DISABLED)
        
        # 进度条
        progress = ttk.Progressbar(ui_window, mode='determinate', value=100)
        progress.pack(fill=tk.X, padx=20, pady=(0, 20))
    
    # 按钮
    ttk.Button(button_frame, text="启动完整界面", 
              command=show_main_interface).pack(side=tk.LEFT, padx=5)
    
    ttk.Button(button_frame, text="功能特性", 
              command=show_features).pack(side=tk.LEFT, padx=5)
    
    ttk.Button(button_frame, text="UI组件演示", 
              command=show_ui_demo).pack(side=tk.LEFT, padx=5)
    
    ttk.Button(button_frame, text="退出演示", 
              command=root.quit).pack(side=tk.LEFT, padx=5)
    
    # 状态栏
    status_frame = ttk.Frame(main_frame)
    status_frame.pack(fill=tk.X, pady=(20, 0))
    
    ttk.Label(status_frame, text="状态: 界面演示就绪").pack(side=tk.LEFT)
    
    # 进度条演示
    progress = ttk.Progressbar(status_frame, mode='indeterminate')
    progress.pack(side=tk.RIGHT, padx=(10, 0))
    
    def animate_progress():
        progress.start(10)
        root.after(3000, progress.stop)
    
    root.after(1000, animate_progress)
    
    return root

if __name__ == "__main__":
    try:
        print("启动界面演示...")
        root = create_demo_window()
        print("界面已创建，正在显示...")
        root.mainloop()
        print("界面演示结束")
    except Exception as e:
        print(f"界面演示失败: {e}")
        import traceback
        traceback.print_exc()
