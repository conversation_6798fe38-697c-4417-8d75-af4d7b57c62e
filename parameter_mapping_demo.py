"""
参数映射演示程序
展示FMW参数到UI组件的完整映射
"""
import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox
import json
import base64

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def create_demo_fmw():
    """创建演示用的FMW文件"""
    test_parameters = {
        "parameters": [
            {
                "name": "INPUT_SHAPEFILE",
                "type": "file",
                "prompt": "输入Shapefile文件",
                "required": True,
                "defaultValue": "",
                "valueType": "string",
                "accessMode": "read",
                "itemsToSelect": "files",
                "filters": [
                    {"name": "Shapefile", "filter": ["*.shp"]},
                    {"name": "所有文件", "filter": ["*.*"]}
                ]
            },
            {
                "name": "OUTPUT_DIRECTORY",
                "type": "file",
                "prompt": "输出目录",
                "required": True,
                "defaultValue": "",
                "valueType": "string",
                "accessMode": "write",
                "itemsToSelect": "folders"
            },
            {
                "name": "COORDINATE_SYSTEM",
                "type": "dropdown",
                "prompt": "目标坐标系",
                "required": False,
                "defaultValue": "EPSG:4326",
                "choiceSettings": {
                    "choices": [
                        {"display": "WGS84 地理坐标系 (EPSG:4326)", "value": "EPSG:4326"},
                        {"display": "Web墨卡托投影 (EPSG:3857)", "value": "EPSG:3857"},
                        {"display": "北京54坐标系 (EPSG:2154)", "value": "EPSG:2154"},
                        {"display": "西安80坐标系 (EPSG:4610)", "value": "EPSG:4610"}
                    ]
                }
            },
            {
                "name": "BUFFER_DISTANCE",
                "type": "number",
                "prompt": "缓冲区距离(米)",
                "required": False,
                "defaultValue": "100",
                "valueType": "float"
            },
            {
                "name": "ENABLE_TOPOLOGY_CHECK",
                "type": "checkbox",
                "prompt": "启用拓扑检查",
                "required": False,
                "defaultValue": "true",
                "valueType": "boolean"
            },
            {
                "name": "PROCESSING_NOTES",
                "type": "textarea",
                "prompt": "处理说明",
                "required": False,
                "defaultValue": "GIS数据处理任务\\n包含坐标转换和缓冲区分析",
                "valueType": "string"
            },
            {
                "name": "QUALITY_LEVEL",
                "type": "listbox",
                "prompt": "质量等级",
                "required": False,
                "defaultValue": "high",
                "singleSelection": True,
                "choiceSettings": {
                    "choices": [
                        {"display": "快速处理", "value": "fast"},
                        {"display": "标准质量", "value": "standard"},
                        {"display": "高质量", "value": "high"},
                        {"display": "最高质量", "value": "premium"}
                    ]
                }
            },
            {
                "name": "FEATURE_TYPES",
                "type": "listbox",
                "prompt": "处理的要素类型",
                "required": False,
                "defaultValue": "",
                "singleSelection": False,
                "delimiter": ",",
                "choiceSettings": {
                    "choices": [
                        {"display": "点要素", "value": "point"},
                        {"display": "线要素", "value": "line"},
                        {"display": "面要素", "value": "polygon"},
                        {"display": "注记", "value": "annotation"}
                    ]
                }
            },
            {
                "name": "PROCESS_DATETIME",
                "type": "datetime",
                "prompt": "处理时间",
                "required": False,
                "defaultValue": "2024-08-12 14:30:00",
                "valueType": "string"
            },
            {
                "name": "HIGHLIGHT_COLOR",
                "type": "color",
                "prompt": "高亮显示颜色",
                "required": False,
                "defaultValue": "#FF6B35",
                "valueType": "string"
            }
        ]
    }
    
    # 编码参数
    params_json = json.dumps(test_parameters, ensure_ascii=False)
    params_base64 = base64.b64encode(params_json.encode('utf-8')).decode('ascii')
    
    # 创建FMW内容
    fmw_content = f'''#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE
#!   ARCGIS_COMPATIBILITY="ARCGIS_AUTO"
#!   ATTR_TYPE_ENCODING="SDF"
#!   CATEGORY="演示"
#!   DESCRIPTION="参数映射演示模型"
#!   DESTINATION="NONE"
#!   FME_BUILD_NUM="23619"
#!   FME_DOCUMENT_GUID="demo-parameter-mapping"
#!   FME_GEOMETRY_HANDLING="Enhanced"
#!   FME_NAMES_ENCODING="UTF-8"
#!   LAST_SAVE_BUILD="FME(R) 2023.1.0.0"
#!   LAST_SAVE_DATE="2024-08-12T14:30:00"
#!   TITLE="GIS数据处理演示模型"
#!   WORKSPACE_VERSION="1"
#! >
#! 
#! <USER_PARAMETERS FORM="{params_base64}">
#! </USER_PARAMETERS>
#! 
#! </WORKSPACE>'''
    
    return fmw_content

class ParameterMappingDemo:
    """参数映射演示类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("FME参数映射演示")
        self.root.geometry("1000x800")
        
        # 居中显示
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1000 // 2)
        y = (self.root.winfo_screenheight() // 2) - (800 // 2)
        self.root.geometry(f"1000x800+{x}+{y}")
        
        self.param_widgets = {}
        self.setup_ui()
        self.load_demo_parameters()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="FME参数映射演示", 
                               font=("Arial", 18, "bold"))
        title_label.pack(pady=(0, 10))
        
        # 说明文本
        info_label = ttk.Label(main_frame, 
                              text="此演示展示了FMW文件中各种参数类型到UI组件的映射效果",
                              font=("Arial", 10))
        info_label.pack(pady=(0, 20))
        
        # 创建左右分栏
        paned = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)
        
        # 左侧 - 参数列表
        left_frame = ttk.LabelFrame(paned, text="参数类型说明", padding="10")
        paned.add(left_frame, weight=1)
        
        info_text = tk.Text(left_frame, height=20, wrap=tk.WORD, width=30)
        info_text.pack(fill=tk.BOTH, expand=True)
        
        info_content = """参数类型映射说明:

📁 file (文件选择)
• 输入文件: 文件选择对话框
• 输出目录: 文件夹选择对话框
• 支持文件类型过滤

📋 dropdown (下拉选择)
• 单选下拉列表
• 预定义选项
• 支持默认值

🔢 number (数字输入)
• 数字验证
• 支持整数和浮点数
• 自动类型转换

☑️ checkbox (复选框)
• 布尔值选择
• 真/假状态

📝 textarea (多行文本)
• 多行文本输入
• 支持换行
• 滚动条支持

📊 listbox (列表选择)
• 单选或多选
• 自定义分隔符
• 多个预定义选项

📅 datetime (日期时间)
• 日期时间选择
• 格式化输入

🎨 color (颜色选择)
• 颜色选择器
• 十六进制颜色值

所有参数都支持:
• 必填验证
• 默认值设置
• 提示文本显示
• 值的获取和设置"""
        
        info_text.insert(1.0, info_content)
        info_text.config(state=tk.DISABLED)
        
        # 右侧 - 参数表单
        right_frame = ttk.LabelFrame(paned, text="参数配置表单", padding="10")
        paned.add(right_frame, weight=2)
        
        # 创建滚动框架
        self.canvas = tk.Canvas(right_frame)
        scrollbar = ttk.Scrollbar(right_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="获取参数值", 
                  command=self.show_parameter_values).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="重置参数", 
                  command=self.reset_parameters).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="启动主程序", 
                  command=self.launch_main_app).pack(side=tk.RIGHT, padx=5)
        
        ttk.Button(button_frame, text="关闭", 
                  command=self.root.destroy).pack(side=tk.RIGHT, padx=5)
    
    def load_demo_parameters(self):
        """加载演示参数"""
        # 创建演示FMW文件
        fmw_content = create_demo_fmw()
        demo_fmw_path = "parameter_mapping_demo.fmw"
        
        with open(demo_fmw_path, 'w', encoding='utf-8') as f:
            f.write(fmw_content)
        
        try:
            # 解析参数
            from parse_fmw import parse_fmw_parameters
            result = parse_fmw_parameters(demo_fmw_path, debug=False)
            
            if result and result.get('parameters'):
                parameters = result['parameters']
                
                # 导入主窗口类来使用其方法
                from main_window import MainWindow
                temp_app = MainWindow()
                
                row = 0
                for param in parameters:
                    param_name = param.get('name', '')
                    param_info = param.get('info', {})
                    
                    if not param_name:
                        continue
                    
                    # 创建标签
                    label_text = param_info.get('prompt', param_name)
                    if param_info.get('required', False):
                        label_text += " *"
                    
                    label = ttk.Label(self.scrollable_frame, text=label_text, 
                                     font=("Arial", 9, "bold"))
                    label.grid(row=row, column=0, sticky=tk.W, padx=5, pady=8)
                    
                    # 添加类型说明
                    type_label = ttk.Label(self.scrollable_frame, 
                                          text=f"({param_info.get('type', 'text')})",
                                          font=("Arial", 8), foreground="gray")
                    type_label.grid(row=row, column=2, sticky=tk.W, padx=5, pady=8)
                    
                    # 创建控件
                    widget = temp_app.create_parameter_widget(self.scrollable_frame, param_info)
                    widget.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
                    
                    self.param_widgets[param_name] = {
                        'widget': widget,
                        'info': param_info,
                        'label': label
                    }
                    
                    row += 1
                
                # 配置列权重
                self.scrollable_frame.columnconfigure(1, weight=1)
                
                print(f"✓ 成功加载 {len(parameters)} 个演示参数")
                
        except Exception as e:
            messagebox.showerror("错误", f"加载演示参数失败: {e}")
            print(f"加载演示参数失败: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            # 清理演示文件
            if os.path.exists(demo_fmw_path):
                os.unlink(demo_fmw_path)
    
    def show_parameter_values(self):
        """显示参数值"""
        try:
            # 使用主窗口的方法获取参数值
            from main_window import MainWindow
            temp_app = MainWindow()
            temp_app.param_widgets = self.param_widgets
            
            values = temp_app.get_parameter_values()
            
            # 创建结果窗口
            result_window = tk.Toplevel(self.root)
            result_window.title("参数值获取结果")
            result_window.geometry("600x500")
            result_window.transient(self.root)
            result_window.grab_set()
            
            # 居中显示
            result_window.update_idletasks()
            x = self.root.winfo_x() + 200
            y = self.root.winfo_y() + 150
            result_window.geometry(f"600x500+{x}+{y}")
            
            # 创建文本显示区域
            text_frame = ttk.Frame(result_window, padding="10")
            text_frame.pack(fill=tk.BOTH, expand=True)
            
            ttk.Label(text_frame, text="获取到的参数值:", 
                     font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(0, 10))
            
            text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Consolas", 10))
            text_scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
            text_widget.configure(yscrollcommand=text_scrollbar.set)
            
            # 格式化显示参数值
            result_text = ""
            for name, value in values.items():
                param_info = self.param_widgets[name]['info']
                param_type = param_info.get('type', 'text')
                prompt = param_info.get('prompt', name)
                
                result_text += f"参数名: {name}\n"
                result_text += f"显示名: {prompt}\n"
                result_text += f"类型: {param_type}\n"
                result_text += f"值: {value}\n"
                result_text += f"值类型: {type(value).__name__}\n"
                result_text += "-" * 50 + "\n\n"
            
            text_widget.insert(1.0, result_text)
            text_widget.config(state=tk.DISABLED)
            
            text_widget.pack(side="left", fill="both", expand=True)
            text_scrollbar.pack(side="right", fill="y")
            
            # 关闭按钮
            ttk.Button(result_window, text="关闭", 
                      command=result_window.destroy).pack(pady=10)
            
        except Exception as e:
            messagebox.showerror("错误", f"获取参数值失败: {e}")
    
    def reset_parameters(self):
        """重置参数"""
        self.load_demo_parameters()
        messagebox.showinfo("提示", "参数已重置为默认值")
    
    def launch_main_app(self):
        """启动主程序"""
        try:
            self.root.destroy()
            from main_window import MainWindow
            app = MainWindow()
            app.run()
        except Exception as e:
            messagebox.showerror("错误", f"启动主程序失败: {e}")
    
    def run(self):
        """运行演示"""
        self.root.mainloop()

if __name__ == "__main__":
    try:
        print("启动参数映射演示...")
        demo = ParameterMappingDemo()
        demo.run()
    except Exception as e:
        print(f"演示启动失败: {e}")
        import traceback
        traceback.print_exc()
