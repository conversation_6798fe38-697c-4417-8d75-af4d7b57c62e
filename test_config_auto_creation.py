"""
测试配置文件自动创建功能
"""
import os
import shutil
from config import Config


def test_config_auto_creation():
    """测试配置文件自动创建"""
    print("=== 测试配置文件自动创建功能 ===\n")
    
    # 备份现有配置文件（如果存在）
    backup_files = []
    config_files = ["app_config.dat", "app_config.json"]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            backup_file = f"{config_file}.backup"
            shutil.copy2(config_file, backup_file)
            backup_files.append((config_file, backup_file))
            os.remove(config_file)
            print(f"备份并删除现有配置文件: {config_file}")
    
    try:
        # 1. 测试首次运行时自动创建配置
        print("\n1. 测试首次运行时自动创建配置...")
        
        # 创建新的配置实例（模拟首次运行）
        config = Config()
        
        # 检查配置是否正确加载
        if config.get("app_name") == "FME模型管理工具":
            print("✓ 默认配置加载成功")
        else:
            print("✗ 默认配置加载失败")
            return False
        
        # 检查是否创建了编码的配置文件
        if os.path.exists("app_config.dat"):
            print("✓ 编码配置文件已创建: app_config.dat")
        else:
            print("✗ 编码配置文件未创建")
            return False
        
        # 检查文件内容是否已编码
        with open("app_config.dat", 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "FME模型管理工具" not in content and "app_name" not in content:
            print("✓ 配置文件内容已编码，无法直接读取")
        else:
            print("✗ 配置文件内容未正确编码")
            return False
        
        # 2. 测试配置项访问
        print("\n2. 测试配置项访问...")
        
        test_cases = [
            ("app_name", "FME模型管理工具"),
            ("app_version", "1.0.0"),
            ("models_dir", "models"),
            ("output_dir", "output"),
            ("theme", "cosmo"),
            ("auto_backup", True),
            ("max_recent_files", 10)
        ]
        
        for key, expected_value in test_cases:
            actual_value = config.get(key)
            if actual_value == expected_value:
                print(f"✓ {key}: {actual_value}")
            else:
                print(f"✗ {key}: 期望 {expected_value}, 实际 {actual_value}")
                return False
        
        # 3. 测试配置项修改和保存
        print("\n3. 测试配置项修改和保存...")
        
        # 修改配置项
        config.set("test_key", "test_value")
        config.set("fme_path", "C:\\Custom\\FME\\fme.exe")
        
        # 重新加载配置验证保存
        config2 = Config()
        
        if config2.get("test_key") == "test_value":
            print("✓ 配置项修改和保存成功")
        else:
            print("✗ 配置项修改和保存失败")
            return False
        
        if config2.get("fme_path") == "C:\\Custom\\FME\\fme.exe":
            print("✓ FME路径修改保存成功")
        else:
            print("✗ FME路径修改保存失败")
            return False
        
        # 4. 测试目录自动创建
        print("\n4. 测试目录自动创建...")
        
        config.ensure_directories()
        
        required_dirs = ["models", "output", "temp", "clients"]
        for dir_name in required_dirs:
            if os.path.exists(dir_name):
                print(f"✓ 目录已创建: {dir_name}")
            else:
                print(f"✗ 目录未创建: {dir_name}")
                return False
        
        # 5. 测试绝对路径转换
        print("\n5. 测试绝对路径转换...")
        
        # 测试相对路径转换
        relative_path = "models"
        absolute_path = config.get_absolute_path(relative_path)
        expected_path = os.path.join(config.get_app_directory(), "models")
        
        if absolute_path == expected_path:
            print(f"✓ 相对路径转换成功: {relative_path} -> {absolute_path}")
        else:
            print(f"✗ 相对路径转换失败: 期望 {expected_path}, 实际 {absolute_path}")
            return False
        
        # 测试绝对路径保持不变
        abs_path = "C:\\Test\\Path"
        result_path = config.get_absolute_path(abs_path)
        
        if result_path == abs_path:
            print(f"✓ 绝对路径保持不变: {abs_path}")
        else:
            print(f"✗ 绝对路径处理错误: 期望 {abs_path}, 实际 {result_path}")
            return False
        
        print("\n=== 配置文件自动创建功能测试完成 ===")
        print("✓ 所有测试通过！")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 恢复备份的配置文件
        for original_file, backup_file in backup_files:
            if os.path.exists(backup_file):
                shutil.copy2(backup_file, original_file)
                os.remove(backup_file)
                print(f"恢复配置文件: {original_file}")
        
        # 清理测试创建的文件和目录
        cleanup_files = ["app_config.dat"]
        cleanup_dirs = ["models", "output", "temp", "clients"]
        
        for file in cleanup_files:
            if os.path.exists(file):
                os.remove(file)
        
        for dir in cleanup_dirs:
            if os.path.exists(dir) and os.path.isdir(dir):
                try:
                    shutil.rmtree(dir)
                except:
                    pass


def test_config_migration():
    """测试配置文件迁移功能"""
    print("\n=== 测试配置文件迁移功能 ===\n")
    
    try:
        # 1. 创建旧版JSON配置文件
        print("1. 创建旧版JSON配置文件...")
        
        old_config = {
            "app_name": "FME模型管理工具",
            "app_version": "1.0.0",
            "fme_path": "C:\\Program Files\\FME\\fme.exe",
            "models_dir": "models",
            "theme": "cosmo"
        }
        
        import json
        with open("app_config.json", 'w', encoding='utf-8') as f:
            json.dump(old_config, f, ensure_ascii=False, indent=2)
        
        print("✓ 旧版JSON配置文件已创建")
        
        # 2. 测试自动迁移
        print("\n2. 测试自动迁移...")
        
        # 创建配置实例，应该自动迁移
        config = Config()
        
        # 检查是否正确加载了旧配置
        if config.get("app_name") == "FME模型管理工具":
            print("✓ 旧配置数据迁移成功")
        else:
            print("✗ 旧配置数据迁移失败")
            return False
        
        # 检查是否创建了新的编码文件
        if os.path.exists("app_config.dat"):
            print("✓ 新的编码配置文件已创建")
        else:
            print("✗ 新的编码配置文件未创建")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 迁移测试失败: {e}")
        return False
    
    finally:
        # 清理测试文件
        test_files = ["app_config.json", "app_config.dat"]
        for file in test_files:
            if os.path.exists(file):
                os.remove(file)


def run_all_config_tests():
    """运行所有配置测试"""
    print("开始测试配置文件自动创建和编码功能...\n")
    
    tests = [
        ("配置文件自动创建", test_config_auto_creation),
        ("配置文件迁移", test_config_migration)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"{'='*50}")
        print(f"运行测试: {test_name}")
        print(f"{'='*50}")
        
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed_tests += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
        
        print()
    
    # 显示测试结果
    print("="*50)
    print("🎯 配置测试结果总结")
    print("="*50)
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"通过率: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有配置测试通过！")
        print("\n✅ 配置功能总结:")
        print("1. ✅ 首次运行自动创建默认配置")
        print("2. ✅ 配置文件使用编码格式保护敏感信息")
        print("3. ✅ 支持配置项的读取和修改")
        print("4. ✅ 自动创建必要的目录结构")
        print("5. ✅ 支持相对路径和绝对路径转换")
        print("6. ✅ 支持旧版JSON配置文件自动迁移")
    else:
        print(f"\n⚠️ 有 {total_tests - passed_tests} 个测试失败，请检查配置功能。")


if __name__ == "__main__":
    run_all_config_tests()
