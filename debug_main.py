"""
调试主窗口问题
"""
import sys
import os
import traceback

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

print("开始调试主窗口...")

try:
    print("1. 导入基础模块...")
    import tkinter as tk
    import ttkbootstrap as ttk_bs
    from config import config
    print("   ✓ 基础模块导入成功")
    
    print("2. 测试主题初始化...")
    style = ttk_bs.Style(theme=config.get("theme", "cosmo"))
    root = style.master
    print("   ✓ 主题初始化成功")
    
    print("3. 导入管理器模块...")
    try:
        from model_manager import ModelManager
        print("   ✓ ModelManager导入成功")
    except Exception as e:
        print(f"   ✗ ModelManager导入失败: {e}")
        traceback.print_exc()
    
    try:
        from fmw_runner import FMWRunner
        print("   ✓ FMWRunner导入成功")
    except Exception as e:
        print(f"   ✗ FMWRunner导入失败: {e}")
        traceback.print_exc()
    
    try:
        from task_manager import TaskManager
        print("   ✓ TaskManager导入成功")
    except Exception as e:
        print(f"   ✗ TaskManager导入失败: {e}")
        traceback.print_exc()
    
    try:
        from client_generator import ClientGenerator
        print("   ✓ ClientGenerator导入成功")
    except Exception as e:
        print(f"   ✗ ClientGenerator导入失败: {e}")
        traceback.print_exc()
    
    print("4. 测试MainWindow导入...")
    from main_window import MainWindow
    print("   ✓ MainWindow类导入成功")
    
    print("5. 创建MainWindow实例...")
    
    # 逐步创建MainWindow
    print("   - 初始化主题...")
    style = ttk_bs.Style(theme=config.get("theme", "cosmo"))
    root = style.master
    root.title(f"FME模型管理工具 v{config.get('app_version', '1.0.0')}")
    print("   ✓ 主题和标题设置成功")
    
    print("   - 设置窗口大小...")
    window_size = config.get("window_size", "1200x800")
    width, height = map(int, window_size.split('x'))
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width // 2) - (width // 2)
    y = (screen_height // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")
    print("   ✓ 窗口大小和位置设置成功")
    
    print("   - 初始化管理器...")
    model_manager = ModelManager()
    print("   ✓ ModelManager初始化成功")
    
    fmw_runner = FMWRunner()
    print("   ✓ FMWRunner初始化成功")
    
    task_manager = TaskManager()
    print("   ✓ TaskManager初始化成功")
    
    client_generator = ClientGenerator()
    print("   ✓ ClientGenerator初始化成功")
    
    print("   - 确保目录存在...")
    config.ensure_directories()
    print("   ✓ 目录检查完成")
    
    print("6. 创建简单界面...")
    # 创建一个简单的界面来测试
    main_frame = ttk_bs.Frame(root, padding=20)
    main_frame.pack(fill="both", expand=True)
    
    title_label = ttk_bs.Label(main_frame, text="FME模型管理工具", font=("Arial", 16, "bold"))
    title_label.pack(pady=20)
    
    status_label = ttk_bs.Label(main_frame, text="界面加载成功！", font=("Arial", 12))
    status_label.pack(pady=10)
    
    close_button = ttk_bs.Button(main_frame, text="关闭", command=root.destroy)
    close_button.pack(pady=20)
    
    print("   ✓ 简单界面创建成功")
    
    print("7. 显示窗口...")
    root.deiconify()  # 确保窗口可见
    root.lift()       # 提升窗口到前台
    root.focus_force() # 强制获取焦点
    
    print("窗口已显示，5秒后自动关闭...")
    root.after(5000, root.destroy)
    
    root.mainloop()
    
    print("测试完成！")
    
except Exception as e:
    print(f"调试过程中发生错误: {e}")
    traceback.print_exc()
