@echo off
chcp 65001 >nul
title FME模型管理工具

echo ========================================
echo FME模型管理工具启动脚本
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 检测到Python环境...

:: 检查是否存在虚拟环境
if not exist "venv" (
    echo 创建虚拟环境...
    python -m venv venv
    if errorlevel 1 (
        echo 错误: 创建虚拟环境失败
        pause
        exit /b 1
    )
)

:: 激活虚拟环境
echo 激活虚拟环境...
call venv\Scripts\activate.bat

:: 安装依赖
echo 检查并安装依赖包...
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

if errorlevel 1 (
    echo 警告: 部分依赖包安装失败，尝试使用默认源...
    pip install -r requirements.txt
)

:: 启动程序
echo.
echo 启动FME模型管理工具...
echo.
python main.py

if errorlevel 1 (
    echo.
    echo 程序异常退出，请检查错误信息
    pause
)

echo.
echo 程序已退出
pause
