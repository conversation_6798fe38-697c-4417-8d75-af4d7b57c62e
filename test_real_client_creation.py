"""
测试真实的客户端创建流程
"""
import sys
import os
import tkinter as tk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_real_client_creation():
    """测试真实的客户端创建流程"""
    # 创建主窗口
    style = ttk_bs.Style(theme="cosmo")
    root = style.master
    root.title("真实客户端创建测试")
    root.geometry("500x400")
    
    # 主窗口居中
    from dialogs import center_window
    center_window(root)
    
    def start_client_creation():
        """启动客户端创建对话框"""
        try:
            from model_manager import ModelManager
            from client_generator import ClientGenerator
            from dialogs import ClientCreateDialog
            
            print("初始化管理器...")
            model_manager = ModelManager()
            client_generator = ClientGenerator()
            
            print("获取模型...")
            models = model_manager.get_all_models()
            if not models:
                tk.messagebox.showerror("错误", "没有找到任何模型")
                return
            
            print(f"找到 {len(models)} 个模型")
            
            # 启动客户端创建对话框
            print("启动客户端创建对话框...")
            ClientCreateDialog(root, models, client_generator)
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            tk.messagebox.showerror("错误", f"启动客户端创建对话框失败：\n{e}")
    
    def test_direct_generation():
        """直接测试客户端生成"""
        try:
            from model_manager import ModelManager
            from client_generator import ClientGenerator
            
            print("初始化管理器...")
            model_manager = ModelManager()
            client_generator = ClientGenerator()
            
            print("获取模型...")
            all_models = model_manager.get_all_models()
            if not all_models:
                tk.messagebox.showerror("错误", "没有找到任何模型")
                return
            
            # 选择第一个模型
            selected_models = [list(all_models.keys())[0]]
            print(f"选择的模型: {selected_models}")
            
            # 客户端信息
            client_info = {
                'name': '直接测试客户端',
                'project': '测试项目',
                'user': '测试用户',
                'contact': '<EMAIL>',
                'description': '这是一个直接测试客户端'
            }
            
            print(f"客户端信息: {client_info}")
            print(f"客户端名称: '{client_info['name']}'")
            
            print("开始生成客户端...")
            
            # 直接调用生成方法
            success, message, zip_path = client_generator.generate_client(
                selected_models, client_info
            )
            
            if success:
                tk.messagebox.showinfo("成功", f"客户端生成成功！\n{message}")
            else:
                tk.messagebox.showerror("失败", f"客户端生成失败：\n{message}")
                
        except Exception as e:
            import traceback
            traceback.print_exc()
            tk.messagebox.showerror("错误", f"生成过程中发生错误：\n{e}")
    
    # 创建界面
    main_frame = ttk_bs.Frame(root, padding=20)
    main_frame.pack(fill=BOTH, expand=True)
    
    title_label = ttk_bs.Label(main_frame, text="真实客户端创建测试", 
                              font=("Arial", 16, "bold"))
    title_label.pack(pady=20)
    
    info_label = ttk_bs.Label(main_frame, 
                             text="测试真实的客户端创建流程\n"
                                  "观察进度对话框的标题是否正确", 
                             font=("Arial", 10), justify=CENTER)
    info_label.pack(pady=10)
    
    button_frame = ttk_bs.Frame(main_frame)
    button_frame.pack(pady=30)
    
    ttk_bs.Button(button_frame, text="启动客户端创建对话框", 
                 command=start_client_creation, bootstyle=PRIMARY,
                 width=25).pack(pady=10)
    
    ttk_bs.Button(button_frame, text="直接测试客户端生成", 
                 command=test_direct_generation, bootstyle=SECONDARY,
                 width=25).pack(pady=10)
    
    status_label = ttk_bs.Label(main_frame, 
                               text="点击按钮开始测试\n"
                                    "观察进度对话框标题是否显示正确的客户端名称", 
                               font=("Arial", 9), foreground="gray")
    status_label.pack(pady=20)
    
    print("真实客户端创建测试程序已启动")
    
    root.mainloop()

if __name__ == "__main__":
    test_real_client_creation()
