"""
测试MainWindow的各个组件
"""
import sys
import os
import traceback

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

print("测试MainWindow组件...")

try:
    from main_window import MainWindow
    
    print("1. 创建MainWindow实例...")
    app = MainWindow()
    print("   ✓ MainWindow实例创建成功")
    
    print("2. 检查窗口属性...")
    print(f"   窗口标题: {app.root.title()}")
    print(f"   窗口几何: {app.root.geometry()}")
    print("   ✓ 窗口属性正常")
    
    print("3. 测试界面组件...")
    
    # 检查是否有必要的属性
    required_attrs = [
        'model_manager', 'fmw_runner', 'task_manager', 'client_generator'
    ]
    
    for attr in required_attrs:
        if hasattr(app, attr):
            print(f"   ✓ {attr} 存在")
        else:
            print(f"   ✗ {attr} 不存在")
    
    print("4. 显示窗口...")
    
    # 添加一个简单的标签来测试界面是否正常
    import ttkbootstrap as ttk_bs
    
    test_label = ttk_bs.Label(app.root, text="界面加载测试", font=("Arial", 16))
    test_label.place(x=50, y=50)
    
    close_button = ttk_bs.Button(app.root, text="关闭测试", command=app.root.destroy)
    close_button.place(x=50, y=100)
    
    print("   窗口将显示3秒...")
    app.root.after(3000, app.root.destroy)
    
    # 强制刷新和显示
    app.root.update()
    app.root.deiconify()
    app.root.lift()
    
    app.root.mainloop()
    
    print("测试完成！")
    
except Exception as e:
    print(f"测试失败: {e}")
    traceback.print_exc()
