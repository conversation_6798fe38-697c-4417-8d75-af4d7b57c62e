"""
调试客户端文件创建问题
"""
import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def debug_client_files():
    """调试客户端文件创建"""
    try:
        from model_manager import ModelManager
        from client_generator import ClientGenerator
        
        print("1. 初始化管理器...")
        model_manager = ModelManager()
        client_generator = ClientGenerator()
        
        print("2. 获取所有模型...")
        all_models = model_manager.get_all_models()
        print(f"   找到 {len(all_models)} 个模型")
        
        if not all_models:
            print("   没有找到任何模型，无法测试客户端创建")
            return
        
        # 选择第一个模型
        selected_models = [list(all_models.keys())[0]]
        print(f"   选择的模型: {selected_models}")
        
        print("3. 准备客户端信息...")
        client_info = {
            'name': '调试客户端',
            'project': '调试项目',
            'user': '调试用户',
            'contact': '<EMAIL>',
            'description': '这是一个调试客户端'
        }
        
        print("4. 开始创建客户端目录和文件...")
        
        # 手动调用generate_client_files来检查文件创建
        import uuid
        import json
        from datetime import datetime
        
        client_id = f"client_{int(datetime.now().timestamp())}"
        client_dir = os.path.join(client_generator.clients_dir, client_id)
        
        print(f"   客户端目录: {client_dir}")
        
        # 创建客户端目录
        os.makedirs(client_dir, exist_ok=True)
        print(f"   ✓ 客户端目录已创建")
        
        # 创建models子目录
        models_dir = os.path.join(client_dir, "models")
        os.makedirs(models_dir, exist_ok=True)
        print(f"   ✓ 模型目录已创建: {models_dir}")
        
        # 复制模型文件
        copied_models = []
        for model_id in selected_models:
            model_info = model_manager.get_model_info(model_id)
            if model_info:
                source_file = model_manager.get_absolute_path(model_info["file_path"])
                dest_filename = model_info.get("hidden_filename", f"model_{model_id}.dat") + ".enc"
                dest_file = os.path.join(models_dir, dest_filename)
                
                print(f"   复制模型: {source_file} -> {dest_file}")
                
                if os.path.exists(source_file):
                    import shutil
                    shutil.copy2(source_file, dest_file)
                    copied_models.append({
                        "id": model_id,
                        "name": model_info["name"],
                        "file_name": dest_filename
                    })
                    print(f"   ✓ 模型文件已复制")
                else:
                    print(f"   ✗ 源文件不存在: {source_file}")
        
        # 创建客户端配置
        client_config = {
            "client_id": client_id,
            "client_name": client_info['name'],
            "models": copied_models,
            "created_at": datetime.now().isoformat()
        }
        
        config_file = os.path.join(client_dir, "client_config.json")
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(client_config, f, ensure_ascii=False, indent=2)
        print(f"   ✓ 配置文件已创建: {config_file}")
        
        # 创建客户端主程序
        client_script = os.path.join(client_dir, "fme_client.py")
        template_content = client_generator.create_client_template(client_info['name'])
        with open(client_script, 'w', encoding='utf-8') as f:
            f.write(template_content)
        print(f"   ✓ 客户端脚本已创建: {client_script}")
        print(f"   脚本大小: {os.path.getsize(client_script)} 字节")
        
        # 复制加密模块
        client_generator.copy_encryption_module(client_dir)
        print(f"   ✓ 加密模块已复制")
        
        # 检查所有文件
        print("\n5. 检查创建的文件:")
        for root, dirs, files in os.walk(client_dir):
            for file in files:
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, client_dir)
                size = os.path.getsize(file_path)
                print(f"   {rel_path}: {size} 字节")
        
        # 测试PyInstaller
        print("\n6. 测试PyInstaller...")
        if os.path.exists(client_script):
            print(f"   客户端脚本存在: {client_script}")
            
            # 尝试PyInstaller
            success, message, exe_path = client_generator.build_client_exe_sync(client_dir, client_info['name'], client_id)
            print(f"   PyInstaller结果: {success}")
            print(f"   消息: {message}")
            if exe_path:
                print(f"   exe路径: {exe_path}")
                print(f"   exe存在: {os.path.exists(exe_path)}")
        else:
            print(f"   ✗ 客户端脚本不存在: {client_script}")
        
        # 清理
        print("\n7. 清理测试文件...")
        if os.path.exists(client_dir):
            import shutil
            shutil.rmtree(client_dir)
            print(f"   ✓ 已清理: {client_dir}")
        
    except Exception as e:
        print(f"调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_client_files()
