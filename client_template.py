"""
FME模型运行客户端
专为最终用户设计的简化版本
"""
import os
import sys
import json
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import subprocess
from datetime import datetime
import platform
import hashlib
import base64
from pathlib import Path
import threading
import time


def get_resource_path(relative_path):
    """获取资源文件路径（兼容PyInstaller打包）"""
    try:
        # PyInstaller创建临时文件夹，并将路径存储在_MEIPASS中
        base_path = sys._MEIPASS
    except AttributeError:
        # 如果不是打包环境，使用当前目录
        base_path = os.path.dirname(os.path.abspath(__file__))

    return os.path.join(base_path, relative_path)

class FMEClient:
    """FME客户端主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("FME模型运行客户端")
        self.root.geometry("800x600")

        # 设置窗口关闭事件处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 居中显示
        self.center_window()

        # 初始化变量
        self.license_data = None
        self.model_licenses = {}  # 多模型许可证
        self.machine_id = self.get_machine_id()
        self.is_registered = False
        self.license_status = {}  # 每个模型的许可证状态

        # 直接显示主界面
        self.show_main_interface()

        # 加载现有许可证
        try:
            self.load_existing_licenses()
        except Exception as e:
            print(f"加载许可证时发生错误: {e}")

        # 更新许可证状态
        try:
            self.update_license_status()
        except Exception as e:
            print(f"更新许可证状态时发生错误: {e}")

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (400)
        y = (self.root.winfo_screenheight() // 2) - (300)
        self.root.geometry(f"800x600+{x}+{y}")

    def on_closing(self):
        """窗口关闭事件处理"""
        try:
            # 停止所有正在运行的任务
            # 这里可以添加任务停止逻辑
            pass
        except Exception as e:
            print(f"关闭时发生错误: {e}")
        finally:
            # 销毁窗口并退出程序
            self.root.destroy()
            sys.exit(0)

    def load_available_models(self):
        """加载可用模型"""
        # 检查模型树是否存在
        if not hasattr(self, 'model_tree'):
            return

        # 清空现有项目
        for item in self.model_tree.get_children():
            self.model_tree.delete(item)

        # 从客户端配置文件读取模型信息
        config_file = get_resource_path("client_config.json")
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    client_config = json.load(f)

                models = client_config.get('models', [])
                for model in models:
                    model_name = model.get('name', '未知模型')
                    model_id = model.get('id', '')
                    file_name = model.get('file_name', '')
                    category = model.get('category', '默认')

                    # 检查模型文件是否存在（支持加密文件）
                    # 优先从exe同目录的models文件夹读取
                    exe_dir = os.path.dirname(sys.executable if getattr(sys, 'frozen', False) else __file__)
                    model_file = os.path.join(exe_dir, "models", file_name)
                    model_exists = False

                    # 如果exe同目录没有，回退到资源路径
                    if not os.path.exists(model_file):
                        model_file = get_resource_path(f"models/{file_name}")

                    if os.path.exists(model_file):
                        model_exists = True
                    else:
                        # 如果原文件不存在，检查是否有对应的加密文件
                        if file_name.endswith('.fmw'):
                            # 尝试查找对应的加密文件
                            encrypted_variants = [
                                file_name.replace('.fmw', '.enc'),
                                file_name.replace('.fmw', '.dat.enc'),
                                file_name + '.enc'
                            ]
                            for variant in encrypted_variants:
                                # 先检查exe同目录
                                variant_path = os.path.join(exe_dir, "models", variant)
                                if not os.path.exists(variant_path):
                                    variant_path = get_resource_path(f"models/{variant}")

                                if os.path.exists(variant_path):
                                    model_file = variant_path
                                    model_exists = True
                                    print(f"找到加密模型文件: {variant_path}")
                                    break

                    if model_exists:
                        # 检查许可证状态
                        status, license_info = self.get_model_license_status(model_id)

                        # 添加到树形视图
                        self.model_tree.insert("", tk.END, text=model_name,
                                             values=(category, status, license_info),
                                             tags=(status.lower(),))
                    else:
                        print(f"警告：模型文件不存在: {model_file} (已检查加密变体)")

            except Exception as e:
                print(f"加载客户端配置失败: {e}")
                # 回退到原来的方法
                self.load_models_from_directory()
        else:
            # 回退到原来的方法
            self.load_models_from_directory()

        # 设置标签颜色
        self.model_tree.tag_configure("已授权", foreground="green")
        self.model_tree.tag_configure("未授权", foreground="red")
        self.model_tree.tag_configure("已过期", foreground="orange")

        if len(self.model_tree.get_children()) == 0:
            self.model_tree.insert("", tk.END, text="没有可用的模型",
                                 values=("", "", ""), tags=("info",))
            self.model_tree.tag_configure("info", foreground="gray")

    def load_models_from_directory(self):
        """从目录加载模型（回退方法）"""
        models_dir = get_resource_path("models")
        if os.path.exists(models_dir):
            for item in os.listdir(models_dir):
                if item.endswith('.fmw') or item.endswith('.encrypted') or item.endswith('.enc'):
                    # 显示友好的模型名称
                    display_name = os.path.splitext(item)[0]
                    if display_name.endswith('.fmw'):
                        display_name = display_name[:-4]
                    elif display_name.endswith('.dat'):
                        display_name = display_name[:-4]

                    # 检查许可证状态
                    status, license_info = self.get_model_license_status(item)

                    # 添加到树形视图
                    self.model_tree.insert("", tk.END, text=display_name,
                                         values=("默认", status, license_info),
                                         tags=(status.lower(),))

    def on_model_select(self, event=None):
        """模型选择事件"""
        selection = self.model_tree.selection()
        if not selection:
            return

        item = selection[0]
        model_name = self.model_tree.item(item)['text']
        if model_name == "没有可用的模型":
            return

        # 创建参数界面
        self.create_parameter_interface(model_name)

        # 启用运行按钮
        if hasattr(self, 'run_button'):
            self.run_button.config(state=tk.NORMAL)

    def show_model_info(self, model_name):
        """显示模型信息"""
        self.model_info_text.config(state=tk.NORMAL)
        self.model_info_text.delete(1.0, tk.END)

        # 从客户端配置中查找模型信息
        model_info = None
        config_file = get_resource_path("client_config.json")

        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    client_config = json.load(f)

                models = client_config.get('models', [])
                for model in models:
                    if model.get('name') == model_name:
                        model_info = model
                        break
            except Exception as e:
                print(f"读取客户端配置失败: {e}")

        if model_info:
            # 查找实际的模型文件
            file_name = model_info.get('file_name', '')
            model_file = get_resource_path(f"models/{file_name}")

            if os.path.exists(model_file):
                file_size = os.path.getsize(model_file)
                file_size_mb = file_size / (1024 * 1024)

                is_encrypted = model_info.get('is_encrypted', False) or file_name.endswith('.enc')

                info_text = f"""模型名称: {model_name}
模型ID: {model_info.get('id', '未知')}
分类: {model_info.get('category', '默认')}
描述: {model_info.get('description', '无描述')}
文件名: {file_name}
文件路径: {model_file}
文件大小: {file_size_mb:.2f} MB
加密状态: {'已加密' if is_encrypted else '未加密'}
修改时间: {datetime.fromtimestamp(os.path.getmtime(model_file)).strftime('%Y-%m-%d %H:%M:%S')}

说明: 此模型用于数据处理和转换"""

                self.model_info_text.insert(1.0, info_text)
            else:
                self.model_info_text.insert(1.0, f"模型文件不存在: {model_file}")
        else:
            self.model_info_text.insert(1.0, f"未找到模型信息: {model_name}")

        self.model_info_text.config(state=tk.DISABLED)

    def create_parameter_interface(self, model_name):
        """创建参数界面"""
        # 清空现有参数控件
        for widget in self.params_scrollable_frame.winfo_children():
            widget.destroy()

        if not hasattr(self, 'param_widgets'):
            self.param_widgets = {}
        self.param_widgets.clear()

        # 获取模型文件路径
        model_file_path = self.get_model_file_path(model_name)
        if not model_file_path:
            ttk.Label(self.params_scrollable_frame, text=f"无法找到模型文件: {model_name}").pack(pady=20)
            return

        # 解析模型参数
        try:
            parameters = self.parse_model_parameters(model_file_path)
            if not parameters:
                ttk.Label(self.params_scrollable_frame, text="此模型没有可配置的参数").pack(pady=20)
            else:
                self.create_parameter_widgets(parameters)
        except Exception as e:
            print(f"解析模型参数失败: {e}")
            ttk.Label(self.params_scrollable_frame, text=f"参数解析失败: {e}").pack(pady=20)

        # 添加运行按钮
        self.add_control_buttons()

    def get_model_file_path(self, model_name):
        """获取模型文件路径"""
        # 从客户端配置中查找模型信息
        config_file = get_resource_path("client_config.json")
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    client_config = json.load(f)

                models = client_config.get('models', [])
                for model in models:
                    if model.get('name') == model_name:
                        file_name = model.get('file_name', '')

                        # 优先从exe同目录的models文件夹读取
                        exe_dir = os.path.dirname(sys.executable if getattr(sys, 'frozen', False) else __file__)
                        model_file = os.path.join(exe_dir, "models", file_name)

                        # 如果exe同目录没有，回退到资源路径
                        if not os.path.exists(model_file):
                            model_file = get_resource_path(f"models/{file_name}")

                        if os.path.exists(model_file):
                            # 如果是加密文件，需要先解密
                            if model.get('is_encrypted', False) or file_name.endswith('.enc'):
                                return self.decrypt_model_file(model_file, model.get('id'))
                            else:
                                return model_file
                        else:
                            print(f"模型文件不存在: {model_file}")
                            return None
            except Exception as e:
                print(f"读取客户端配置失败: {e}")

        return None

    def decrypt_model_file(self, encrypted_file_path, model_id):
        """解密模型文件"""
        try:
            from encryption import ModelEncryption

            # 创建临时解密文件
            temp_dir = os.path.join(os.path.dirname(encrypted_file_path), "temp")
            os.makedirs(temp_dir, exist_ok=True)

            temp_file = os.path.join(temp_dir, f"{model_id}_temp.fmw")

            # 解密文件
            encryption = ModelEncryption()
            if encryption.decrypt_file(encrypted_file_path, temp_file):
                return temp_file
            else:
                print(f"解密模型文件失败: {encrypted_file_path}")
                return None

        except Exception as e:
            print(f"解密模型文件时发生错误: {e}")
            return None

    def parse_model_parameters(self, model_file_path):
        """解析模型参数"""
        try:
            # 导入参数解析模块
            from parse_fmw import parse_fmw_parameters

            result = parse_fmw_parameters(model_file_path, debug=False)
            if result and result.get('parameters'):
                return result['parameters']
            else:
                print(f"未找到参数信息: {model_file_path}")
                return []

        except Exception as e:
            print(f"解析FMW参数失败: {e}")
            return []

    def create_parameter_widgets(self, parameters):
        """创建参数控件"""
        row = 0

        for param in parameters:
            param_name = param.get('name', '')
            param_info = param.get('info', {})

            if not param_name:
                continue

            # 创建参数标签
            label_text = param_info.get('prompt', param_name)
            if param_info.get('required', False):
                label_text += " *"

            label = ttk.Label(self.params_scrollable_frame, text=label_text)
            label.grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)

            # 根据参数类型创建输入控件
            widget = self.create_parameter_widget(self.params_scrollable_frame, param_info)
            widget.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)

            # 保存控件引用
            self.param_widgets[param_name] = {
                'widget': widget,
                'info': param_info,
                'label': label
            }

            row += 1

        # 配置列权重
        self.params_scrollable_frame.columnconfigure(1, weight=1)

    def create_parameter_widget(self, parent, param_info):
        """根据参数类型创建输入控件"""
        param_type = param_info.get('type', 'text')
        default_value = param_info.get('default_value', '')
        access_mode = param_info.get('access_mode', 'read')

        # 文件/文件夹选择参数
        if param_type == 'file':
            frame = ttk.Frame(parent)

            var = tk.StringVar(value=default_value)
            entry = ttk.Entry(frame, textvariable=var)
            entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

            def browse_file():
                if param_info.get('is_folder', False) or access_mode == 'write':
                    # 文件夹选择或输出路径
                    path = filedialog.askdirectory(title="选择文件夹")
                else:
                    # 文件选择
                    file_types = param_info.get('file_types', ['*.*'])
                    if file_types and file_types != ['*.*']:
                        # 构建文件类型过滤器
                        filetypes = []
                        for ft in file_types:
                            if ft.startswith('*.'):
                                ext = ft[2:]
                                filetypes.append((f"{ext.upper()}文件", ft))
                        filetypes.append(("所有文件", "*.*"))
                    else:
                        filetypes = [("所有文件", "*.*")]

                    path = filedialog.askopenfilename(title="选择文件", filetypes=filetypes)

                if path:
                    var.set(path)

            ttk.Button(frame, text="浏览", command=browse_file).pack(side=tk.RIGHT, padx=(5, 0))
            frame.var = var  # 保存变量引用
            return frame

        # 下拉选择参数
        elif param_type in ['dropdown', 'listbox']:
            options = param_info.get('options', [])
            if options:
                values = []
                for opt in options:
                    if isinstance(opt, dict):
                        values.append(opt.get('value', opt.get('label', '')))
                    else:
                        values.append(str(opt))

                combo = ttk.Combobox(parent, values=values, state="readonly")
                if default_value and default_value in values:
                    combo.set(default_value)
                elif values:
                    combo.set(values[0])
                return combo
            else:
                # 没有选项，使用文本输入
                var = tk.StringVar(value=default_value)
                return ttk.Entry(parent, textvariable=var)

        # 数字输入参数
        elif param_type in ['number', 'integer', 'float']:
            var = tk.StringVar(value=str(default_value) if default_value else '')
            entry = ttk.Entry(parent, textvariable=var)
            return entry

        # 布尔选择参数
        elif param_type in ['checkbox', 'boolean']:
            var = tk.BooleanVar()
            if default_value:
                if isinstance(default_value, str):
                    var.set(default_value.lower() in ['true', '1', 'yes', 'on'])
                else:
                    var.set(bool(default_value))

            checkbutton = ttk.Checkbutton(parent, variable=var)
            checkbutton.var = var  # 保存变量引用
            return checkbutton

        # 默认文本输入参数
        else:
            var = tk.StringVar(value=str(default_value) if default_value else '')
            entry = ttk.Entry(parent, textvariable=var)
            return entry

    def add_control_buttons(self):
        """添加控制按钮"""
        # 获取当前行数
        row = len(self.param_widgets)

        # 运行按钮框架
        button_frame = ttk.Frame(self.params_scrollable_frame)
        button_frame.grid(row=row, column=0, columnspan=2, pady=20)

        self.run_button = ttk.Button(button_frame, text="运行模型",
                                   command=self.run_selected_model,
                                   state=tk.NORMAL)
        self.run_button.pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="重置参数",
                  command=self.reset_parameters).pack(side=tk.LEFT, padx=5)

    def get_machine_id(self):
        """获取机器唯一标识"""
        machine_info = f"{platform.node()}-{platform.machine()}-{platform.processor()}"
        return hashlib.md5(machine_info.encode()).hexdigest()

    def get_parameter_values(self):
        """获取参数值"""
        values = {}

        for param_name, param_data in self.param_widgets.items():
            try:
                widget = param_data['widget']
                param_info = param_data['info']
                param_type = param_info.get('type', 'text')
                value = None

                # 根据参数类型获取值
                if param_type == 'file':
                    # 文件/文件夹选择
                    if hasattr(widget, 'var'):
                        value = widget.var.get()

                elif param_type in ['dropdown', 'listbox']:
                    # 下拉选择
                    if isinstance(widget, ttk.Combobox):
                        value = widget.get()
                    else:
                        value = widget.get() if hasattr(widget, 'get') else ''

                elif param_type in ['checkbox', 'boolean']:
                    # 布尔值
                    if hasattr(widget, 'var'):
                        value = widget.var.get()
                    elif isinstance(widget, ttk.Checkbutton):
                        value = widget.instate(['selected'])

                elif param_type in ['number', 'integer', 'float']:
                    # 数字
                    if hasattr(widget, 'get'):
                        raw_value = widget.get()
                        if raw_value:
                            try:
                                if param_type == 'integer':
                                    value = int(raw_value)
                                elif param_type == 'float':
                                    value = float(raw_value)
                                else:
                                    value = float(raw_value)
                            except ValueError:
                                value = raw_value  # 保留原始值，让FME处理错误
                        else:
                            value = ''

                else:
                    # 默认文本输入
                    if hasattr(widget, 'get'):
                        value = widget.get()
                    elif hasattr(widget, 'var'):
                        value = widget.var.get()
                    else:
                        # 尝试获取textvariable
                        try:
                            if hasattr(widget, 'cget'):
                                var = widget.cget('textvariable')
                                if var:
                                    value = widget.tk.globalgetvar(var)
                        except:
                            pass

                # 存储参数值
                if value is not None:
                    values[param_name] = value
                    print(f"参数 {param_name} = {value} (类型: {param_type})")

            except Exception as e:
                print(f"获取参数 {param_name} 值失败: {e}")

        return values

    def reset_parameters(self):
        """重置参数"""
        for param_name, param_data in self.param_widgets.items():
            try:
                widget = param_data['widget']
                param_info = param_data['info']
                default_value = param_info.get('default_value', '')

                if hasattr(widget, 'var'):
                    widget.var.set(default_value)
                elif hasattr(widget, 'set'):
                    widget.set(default_value)
                elif hasattr(widget, 'delete') and hasattr(widget, 'insert'):
                    widget.delete(0, tk.END)
                    widget.insert(0, str(default_value))
            except Exception as e:
                print(f"重置参数 {param_name} 失败: {e}")

    def run_selected_model(self):
        """运行选中的模型"""
        # 获取选中的模型
        selection = self.model_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个模型")
            return

        item = selection[0]
        model_name = self.model_tree.item(item)['text']

        # 检查许可证状态
        status = self.model_tree.item(item)['values'][1]  # 状态列
        if status != "已授权":
            messagebox.showerror("许可证错误", f"模型 '{model_name}' 未授权或许可证已过期，无法运行。\n\n请导入有效的许可证文件。")
            return

        # 获取参数值
        parameters = self.get_parameter_values()

        # 验证必填参数
        missing_params = []
        for param_name, param_data in self.param_widgets.items():
            param_info = param_data['info']
            if param_info.get('required', False):
                if param_name not in parameters or not parameters[param_name]:
                    missing_params.append(param_info.get('prompt', param_name))

        if missing_params:
            messagebox.showerror("参数错误", f"以下必填参数未填写:\n" + "\n".join(missing_params))
            return

        # 运行模型
        self.execute_fme_model(model_name, parameters)

    def check_registration(self):
        """检查注册状态"""
        try:
            # 优先检查多模型许可证文件（检查exe同目录）
            exe_dir_license = os.path.join(os.path.dirname(sys.executable if getattr(sys, 'frozen', False) else __file__), "license.lic")
            current_dir_license = "license.lic"

            # 按优先级检查许可证文件
            for license_path in [exe_dir_license, current_dir_license]:
                if os.path.exists(license_path):
                    if self.load_multi_model_license_from_path(license_path):
                        self.is_registered = True
                        return True

            # 兼容旧版单一许可证文件
            for license_path in [os.path.join(os.path.dirname(sys.executable if getattr(sys, 'frozen', False) else __file__), "license.dat"), "license.dat"]:
                if os.path.exists(license_path):
                    if self.load_legacy_license_from_path(license_path):
                        self.is_registered = True
                        return True
        except Exception as e:
            print(f"检查注册状态失败: {e}")

        return False

    def execute_fme_model(self, model_name, parameters):
        """执行FME模型"""
        try:
            # 获取FME路径
            config_file = get_resource_path("client_config.json")
            fme_path = "fme.exe"  # 默认值

            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        client_config = json.load(f)
                    fme_path = client_config.get('fme_path', 'fme.exe')
                except:
                    pass

            # 获取模型文件路径
            model_file_path = self.get_model_file_path(model_name)
            if not model_file_path:
                messagebox.showerror("错误", f"无法找到模型文件: {model_name}")
                return

            # 构建FME命令
            cmd = [fme_path, model_file_path]

            # 添加参数
            for param_name, param_value in parameters.items():
                if param_value:  # 只添加非空参数
                    cmd.extend([f"--{param_name}", str(param_value)])

            # 创建输出目录
            output_dir = os.path.join(os.getcwd(), "output",
                                    datetime.now().strftime("%Y%m%d_%H%M%S"))
            os.makedirs(output_dir, exist_ok=True)

            # 添加默认输出路径（如果没有指定）
            if 'DEST_DATASET' not in parameters and 'OUTPUT_DIR' not in parameters:
                cmd.extend(["--DEST_DATASET", output_dir])

            print(f"执行FME命令: {' '.join(cmd)}")

            # 禁用运行按钮
            if hasattr(self, 'run_button'):
                self.run_button.config(state=tk.DISABLED, text="运行中...")

            # 在新线程中运行FME
            import threading

            def run_fme():
                try:
                    import subprocess
                    result = subprocess.run(cmd, capture_output=True, text=True,
                                          encoding='utf-8', timeout=3600)  # 1小时超时

                    # 在主线程中处理结果
                    self.root.after(0, lambda: self.on_fme_complete(result, output_dir, model_name))

                except subprocess.TimeoutExpired:
                    self.root.after(0, lambda: self.on_fme_error("FME执行超时（超过1小时）"))
                except Exception as e:
                    self.root.after(0, lambda: self.on_fme_error(f"FME执行失败: {e}"))

            thread = threading.Thread(target=run_fme)
            thread.daemon = True
            thread.start()

            # 更新许可证使用次数
            self.update_model_license_usage(model_name)

        except Exception as e:
            messagebox.showerror("错误", f"启动FME失败: {e}")
            if hasattr(self, 'run_button'):
                self.run_button.config(state=tk.NORMAL, text="运行模型")

    def on_fme_complete(self, result, output_dir, model_name):
        """FME执行完成回调"""
        # 恢复运行按钮
        if hasattr(self, 'run_button'):
            self.run_button.config(state=tk.NORMAL, text="运行模型")

        if result.returncode == 0:
            # 执行成功
            message = f"模型 '{model_name}' 运行完成！"
            if result.stdout:
                print(f"FME输出: {result.stdout}")

            if messagebox.askyesno("完成", f"{message}\n\n是否打开输出目录？"):
                try:
                    os.startfile(output_dir)
                except:
                    print(f"无法打开输出目录: {output_dir}")
        else:
            # 执行失败
            error_msg = f"模型运行失败 (返回码: {result.returncode})"
            if result.stderr:
                error_msg += f"\n错误信息: {result.stderr}"

            print(f"FME错误: {error_msg}")
            messagebox.showerror("错误", error_msg)

    def on_fme_error(self, error_message):
        """FME执行错误回调"""
        # 恢复运行按钮
        if hasattr(self, 'run_button'):
            self.run_button.config(state=tk.NORMAL, text="运行模型")

        print(f"FME执行错误: {error_message}")
        messagebox.showerror("错误", error_message)

    def load_multi_model_license(self):
        """加载多模型许可证文件（从资源路径）"""
        license_file = get_resource_path("license.lic")
        return self.load_multi_model_license_from_path(license_file)

    def load_multi_model_license_from_path(self, license_file):
        """从指定路径加载多模型许可证文件"""
        try:
            from encryption import LicenseManager

            license_manager = LicenseManager()
            self.model_licenses = license_manager.load_multi_model_license(license_file)

            if self.model_licenses:
                # 验证至少有一个有效的模型许可证
                for model_id, license_data in self.model_licenses.items():
                    valid, message = license_manager.validate_license(license_data)
                    if valid:
                        # 设置主许可证数据为第一个有效的模型许可证（用于向后兼容）
                        if not self.license_data:
                            self.license_data = license_data.copy()
                            self.license_data['client_name'] = license_data.get('model_name', '多模型客户端')
                        print(f"成功加载许可证文件: {license_file}")
                        return True

            return False

        except Exception as e:
            print(f"加载多模型许可证失败: {e}")
            return False

    def load_legacy_license(self):
        """加载旧版单一许可证文件（从资源路径）"""
        license_file = get_resource_path("license.dat")
        return self.load_legacy_license_from_path(license_file)

    def load_legacy_license_from_path(self, license_file):
        """从指定路径加载旧版单一许可证文件"""
        try:
            with open(license_file, "rb") as f:
                encrypted_data = base64.b64decode(f.read())

            # 简单解密（实际应用中应使用更安全的方法）
            key = b"FME_CLIENT_KEY_2024"  # 固定密钥
            decrypted_data = bytes(a ^ b for a, b in zip(encrypted_data, key * (len(encrypted_data) // len(key) + 1)))

            self.license_data = json.loads(decrypted_data.decode())

            # 验证许可证
            if self.validate_license():
                print(f"成功加载旧版许可证文件: {license_file}")
                return True

        except Exception as e:
            print(f"加载旧版许可证失败: {e}")

        return False

    def load_existing_licenses(self):
        """加载现有的许可证文件"""
        try:
            # 尝试加载多模型许可证文件
            license_file = get_resource_path("license.lic")
            if os.path.exists(license_file):
                from encryption import LicenseManager
                license_manager = LicenseManager()
                self.model_licenses = license_manager.load_multi_model_license(license_file)

                if self.model_licenses:
                    print(f"加载了 {len(self.model_licenses)} 个模型许可证")
                    return True

            # 尝试加载旧版许可证文件
            legacy_file = get_resource_path("license.dat")
            if os.path.exists(legacy_file):
                if self.load_legacy_license():
                    return True

        except Exception as e:
            print(f"加载现有许可证失败: {e}")

        return False

    def get_model_license_status(self, model_file):
        """获取模型的许可证状态"""
        # 从文件名推断模型ID（这里需要根据实际情况调整）
        model_id = self.extract_model_id_from_filename(model_file)

        if not self.model_licenses or model_id not in self.model_licenses:
            return "未授权", "无许可证"

        try:
            from encryption import LicenseManager
            license_manager = LicenseManager()
            license_data = self.model_licenses[model_id]

            valid, message = license_manager.validate_license(license_data)

            if valid:
                # 检查过期时间
                if license_data.get("expire_date"):
                    from datetime import datetime
                    expire_date = datetime.fromisoformat(license_data["expire_date"])
                    expire_str = expire_date.strftime("%Y-%m-%d")
                    return "已授权", f"到期: {expire_str}"
                else:
                    return "已授权", "永久许可"
            else:
                if "过期" in message:
                    return "已过期", message
                else:
                    return "未授权", message

        except Exception as e:
            return "未授权", f"验证失败: {e}"

    def extract_model_id_from_filename(self, filename):
        """从文件名提取模型ID"""
        # 这里需要根据实际的文件命名规则来实现
        # 假设文件名格式为 model_xxx.dat.enc，其中xxx是模型ID的一部分
        if filename.startswith("model_") and ".dat" in filename:
            # 提取model_后面的部分作为模型ID的线索
            base_name = filename.split(".")[0]  # model_xxx
            return base_name.replace("model_", "")

        return filename

    def update_license_status(self):
        """更新许可证状态显示"""
        if self.model_licenses:
            valid_count = 0
            total_count = len(self.model_licenses)

            for model_id, license_data in self.model_licenses.items():
                if license_data:  # 确保license_data不为None
                    try:
                        from encryption import LicenseManager
                        license_manager = LicenseManager()
                        valid, _ = license_manager.validate_license(license_data)
                        if valid:
                            valid_count += 1
                    except:
                        pass

            status_text = f"许可证: {valid_count}/{total_count} 个模型已授权"
            color = "green" if valid_count > 0 else "orange"
        else:
            status_text = "许可证: 未导入"
            color = "red"

        if hasattr(self, 'license_status_label'):
            self.license_status_label.config(text=status_text, foreground=color)

    def import_license_file(self):
        """导入许可证文件"""
        from tkinter import filedialog, messagebox

        file_path = filedialog.askopenfilename(
            title="选择许可证文件",
            filetypes=[("许可证文件", "*.lic"), ("所有文件", "*.*")]
        )

        if not file_path:
            return

        try:
            from encryption import LicenseManager
            license_manager = LicenseManager()

            # 加载新的许可证文件
            new_licenses = license_manager.load_multi_model_license(file_path)

            if not new_licenses:
                messagebox.showerror("错误", "无法加载许可证文件，请检查文件格式。")
                return

            # 合并许可证（处理更新情况）
            if not self.model_licenses:
                self.model_licenses = {}

            updated_count = 0
            new_count = 0

            for model_id, new_license in new_licenses.items():
                if model_id in self.model_licenses:
                    # 检查是否需要更新
                    old_license = self.model_licenses[model_id]
                    old_created = old_license.get("created_at", "")
                    new_created = new_license.get("created_at", "")

                    if new_created > old_created:
                        # 合并使用次数
                        old_uses = old_license.get("current_uses", 0)
                        new_license["current_uses"] = old_uses

                        self.model_licenses[model_id] = new_license
                        updated_count += 1
                else:
                    self.model_licenses[model_id] = new_license
                    new_count += 1

            # 保存合并后的许可证到exe同目录
            exe_dir = os.path.dirname(sys.executable if getattr(sys, 'frozen', False) else __file__)
            local_license_file = os.path.join(exe_dir, "license.lic")
            license_manager.save_multi_model_license(self.model_licenses, local_license_file)

            # 更新界面
            self.load_available_models()
            self.update_license_status()

            # 显示结果
            message = f"许可证导入成功！\n新增: {new_count} 个模型\n更新: {updated_count} 个模型"
            messagebox.showinfo("导入成功", message)

        except Exception as e:
            messagebox.showerror("错误", f"导入许可证文件失败: {e}")

    def validate_license(self):
        """验证许可证"""
        if not self.license_data:
            return False

        try:
            # 检查过期时间
            if "expire_date" in self.license_data:
                expire_date = datetime.fromisoformat(self.license_data["expire_date"])
                if datetime.now() > expire_date:
                    messagebox.showerror("许可证过期", "许可证已过期，请联系管理员")
                    return False

            # 检查使用次数
            if "max_uses" in self.license_data:
                current_uses = self.license_data.get("current_uses", 0)
                max_uses = self.license_data["max_uses"]
                if current_uses >= max_uses:
                    messagebox.showerror("使用次数超限", "许可证使用次数已达上限")
                    return False

            # 检查机器码
            if "allowed_machines" in self.license_data:
                if self.machine_id not in self.license_data["allowed_machines"]:
                    messagebox.showerror("机器码不匹配", "此机器无权使用该客户端")
                    return False

            return True

        except Exception as e:
            print(f"验证许可证失败: {e}")
            return False
    
    def show_registration_interface(self):
        """显示注册界面"""
        # 清空窗口
        for widget in self.root.winfo_children():
            widget.destroy()

        # 主框架
        main_frame = ttk.Frame(self.root, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="FME模型运行客户端",
                               font=("Arial", 18, "bold"))
        title_label.pack(pady=(0, 30))

        # 机器码显示
        machine_frame = ttk.LabelFrame(main_frame, text="机器信息", padding=15)
        machine_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Label(machine_frame, text="机器码:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        machine_code_label = ttk.Label(machine_frame, text=self.machine_id,
                                      font=("Consolas", 12), foreground="blue")
        machine_code_label.pack(anchor=tk.W, pady=(5, 10))

        ttk.Label(machine_frame, text="请将此机器码提供给管理员以获取注册码",
                 font=("Arial", 9), foreground="gray").pack(anchor=tk.W)

        # 复制机器码按钮
        def copy_machine_id():
            self.root.clipboard_clear()
            self.root.clipboard_append(self.machine_id)
            messagebox.showinfo("提示", "机器码已复制到剪贴板")

        ttk.Button(machine_frame, text="复制机器码",
                  command=copy_machine_id).pack(anchor=tk.W, pady=(10, 0))

        # 注册码输入
        reg_frame = ttk.LabelFrame(main_frame, text="注册激活", padding=15)
        reg_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Label(reg_frame, text="注册码:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))

        self.reg_code_var = tk.StringVar()
        reg_entry = ttk.Entry(reg_frame, textvariable=self.reg_code_var,
                             font=("Consolas", 10), width=50)
        reg_entry.pack(fill=tk.X, pady=(0, 15))

        # 注册按钮
        button_frame = ttk.Frame(reg_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="激活",
                  command=self.activate_license).pack(side=tk.LEFT)

        ttk.Button(button_frame, text="退出",
                  command=self.root.quit).pack(side=tk.RIGHT)

        # 状态信息
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=(20, 0))

        self.status_label = ttk.Label(status_frame, text="请输入注册码进行激活",
                                     font=("Arial", 9), foreground="orange")
        self.status_label.pack()

    def activate_license(self):
        """激活许可证"""
        reg_code = self.reg_code_var.get().strip()
        if not reg_code:
            messagebox.showwarning("警告", "请输入注册码")
            return

        try:
            self.status_label.config(text="正在验证注册码...", foreground="blue")
            self.root.update()

            # 解析注册码（Base64编码的JSON）
            try:
                decoded_data = base64.b64decode(reg_code.encode())
                license_data = json.loads(decoded_data.decode())
            except:
                messagebox.showerror("错误", "注册码格式无效")
                self.status_label.config(text="注册码格式无效", foreground="red")
                return

            # 验证机器码
            if "allowed_machines" in license_data:
                if self.machine_id not in license_data["allowed_machines"]:
                    messagebox.showerror("错误", "注册码与当前机器不匹配")
                    self.status_label.config(text="机器码不匹配", foreground="red")
                    return

            # 保存许可证
            self.license_data = license_data
            self.save_license()

            messagebox.showinfo("成功", "激活成功！程序将重新启动")
            self.is_registered = True
            self.show_main_interface()

        except Exception as e:
            messagebox.showerror("错误", f"激活失败: {e}")
            self.status_label.config(text="激活失败", foreground="red")

    def save_license(self):
        """保存许可证到本地"""
        try:
            license_json = json.dumps(self.license_data)

            # 简单加密
            key = b"FME_CLIENT_KEY_2024"
            encrypted_data = bytes(a ^ b for a, b in zip(license_json.encode(), key * (len(license_json) // len(key) + 1)))

            with open("license.dat", "wb") as f:
                f.write(base64.b64encode(encrypted_data))

        except Exception as e:
            print(f"保存许可证失败: {e}")
    
    def show_main_interface(self):
        """显示主界面"""
        # 清空窗口
        for widget in self.root.winfo_children():
            widget.destroy()

        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题栏
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(title_frame, text="FME模型运行客户端",
                 font=("Arial", 16, "bold")).pack(side=tk.LEFT)

        # 许可证状态信息
        self.license_status_label = ttk.Label(title_frame, text="许可证: 未导入",
                                            font=("Arial", 9), foreground="red")
        self.license_status_label.pack(side=tk.RIGHT)

        # 导入许可证按钮
        import_license_btn = ttk.Button(title_frame, text="导入许可证",
                                      command=self.import_license_file)
        import_license_btn.pack(side=tk.RIGHT, padx=(0, 10))

        # 创建选项卡
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)

        # 模型运行选项卡
        self.create_model_tab(notebook)

        # 许可证管理选项卡
        self.create_license_tab(notebook)

        # 运行日志选项卡
        self.create_log_tab(notebook)

        # 系统信息选项卡
        self.create_info_tab(notebook)

    def create_model_tab(self, notebook):
        """创建模型运行选项卡"""
        model_frame = ttk.Frame(notebook)
        notebook.add(model_frame, text="模型运行")

        # 左右分栏
        paned = ttk.PanedWindow(model_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 左侧 - 模型选择
        left_frame = ttk.LabelFrame(paned, text="模型选择", padding=10)
        paned.add(left_frame, weight=1)

        ttk.Label(left_frame, text="可用模型:").pack(anchor=tk.W, pady=(0, 5))

        # 模型列表（使用Treeview显示许可证状态）
        columns = ("分类", "状态", "许可信息")
        self.model_tree = ttk.Treeview(left_frame, columns=columns, show="tree headings", height=8)

        # 设置列标题
        self.model_tree.heading("#0", text="模型名称")
        self.model_tree.heading("分类", text="分类")
        self.model_tree.heading("状态", text="状态")
        self.model_tree.heading("许可信息", text="许可信息")

        # 设置列宽
        self.model_tree.column("#0", width=120)
        self.model_tree.column("分类", width=80)
        self.model_tree.column("状态", width=80)
        self.model_tree.column("许可信息", width=120)

        self.model_tree.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        self.model_tree.bind('<<TreeviewSelect>>', self.on_model_select)

        # 加载模型
        self.load_available_models()

        # 右侧 - 参数设置
        right_frame = ttk.LabelFrame(paned, text="参数设置", padding=10)
        paned.add(right_frame, weight=2)

        # 参数滚动框架
        self.params_canvas = tk.Canvas(right_frame, height=300)
        params_scrollbar = ttk.Scrollbar(right_frame, orient="vertical",
                                        command=self.params_canvas.yview)
        self.params_scrollable_frame = ttk.Frame(self.params_canvas)

        self.params_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.params_canvas.configure(scrollregion=self.params_canvas.bbox("all"))
        )

        self.params_canvas.create_window((0, 0), window=self.params_scrollable_frame, anchor="nw")
        self.params_canvas.configure(yscrollcommand=params_scrollbar.set)

        self.params_canvas.pack(side="left", fill="both", expand=True)
        params_scrollbar.pack(side="right", fill="y")

        # 运行按钮
        button_frame = ttk.Frame(right_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        self.run_button = ttk.Button(button_frame, text="运行模型",
                                    command=self.run_selected_model, state=tk.DISABLED)
        self.run_button.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="重置参数",
                  command=self.reset_parameters).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="设置FME路径",
                  command=self.set_fme_path).pack(side=tk.LEFT)

    def create_license_tab(self, notebook):
        """创建许可证管理选项卡"""
        license_frame = ttk.Frame(notebook)
        notebook.add(license_frame, text="许可证管理")

        # 主框架
        main_frame = ttk.Frame(license_frame, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        ttk.Label(main_frame, text="许可证管理", font=("Arial", 14, "bold")).pack(pady=(0, 20))

        # 操作按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Button(button_frame, text="导入许可证文件",
                  command=self.import_license_file).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="刷新状态",
                  command=self.refresh_license_status).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="导出机器码",
                  command=self.export_machine_id).pack(side=tk.LEFT)

        # 许可证状态显示
        status_frame = ttk.LabelFrame(main_frame, text="许可证状态", padding=10)
        status_frame.pack(fill=tk.BOTH, expand=True)

        # 许可证列表
        columns = ("模型名称", "状态", "过期时间", "使用次数", "剩余次数")
        self.license_tree = ttk.Treeview(status_frame, columns=columns, show="headings", height=10)

        # 设置列标题
        for col in columns:
            self.license_tree.heading(col, text=col)

        # 设置列宽
        self.license_tree.column("模型名称", width=150)
        self.license_tree.column("状态", width=80)
        self.license_tree.column("过期时间", width=100)
        self.license_tree.column("使用次数", width=80)
        self.license_tree.column("剩余次数", width=80)

        # 滚动条
        license_scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.license_tree.yview)
        self.license_tree.configure(yscrollcommand=license_scrollbar.set)

        self.license_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        license_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 详细信息框架
        detail_frame = ttk.LabelFrame(main_frame, text="详细信息", padding=10)
        detail_frame.pack(fill=tk.X, pady=(20, 0))

        self.license_detail_text = tk.Text(detail_frame, height=6, wrap=tk.WORD, state=tk.DISABLED)
        detail_scrollbar = ttk.Scrollbar(detail_frame, orient=tk.VERTICAL, command=self.license_detail_text.yview)
        self.license_detail_text.configure(yscrollcommand=detail_scrollbar.set)

        self.license_detail_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        detail_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定选择事件
        self.license_tree.bind('<<TreeviewSelect>>', self.on_license_select)

        # 初始化许可证显示
        self.refresh_license_display()

    def refresh_license_status(self):
        """刷新许可证状态"""
        self.load_existing_licenses()
        self.update_license_status()
        self.load_available_models()
        self.refresh_license_display()

    def refresh_license_display(self):
        """刷新许可证显示"""
        # 检查许可证树是否存在
        if not hasattr(self, 'license_tree'):
            return

        # 清空现有项目
        for item in self.license_tree.get_children():
            self.license_tree.delete(item)

        if not self.model_licenses:
            self.license_tree.insert("", tk.END, values=("无许可证", "", "", "", ""))
            return

        try:
            from encryption import LicenseManager
            license_manager = LicenseManager()

            for model_id, license_data in self.model_licenses.items():
                model_name = license_data.get("model_name", model_id)

                # 验证许可证
                valid, message = license_manager.validate_license(license_data)
                status = "有效" if valid else "无效"

                # 过期时间
                expire_date = license_data.get("expire_date")
                if expire_date:
                    from datetime import datetime
                    expire_dt = datetime.fromisoformat(expire_date)
                    expire_str = expire_dt.strftime("%Y-%m-%d")
                else:
                    expire_str = "永久"

                # 使用次数
                current_uses = license_data.get("current_uses", 0)
                max_uses = license_data.get("max_uses")

                if max_uses:
                    remaining = max_uses - current_uses
                    uses_str = str(current_uses)
                    remaining_str = str(remaining)
                else:
                    uses_str = str(current_uses)
                    remaining_str = "无限制"

                # 添加到列表
                self.license_tree.insert("", tk.END, values=(
                    model_name, status, expire_str, uses_str, remaining_str
                ))

        except Exception as e:
            print(f"刷新许可证显示失败: {e}")

    def on_license_select(self, event=None):
        """许可证选择事件"""
        # 检查必要的组件是否存在
        if not hasattr(self, 'license_tree') or not hasattr(self, 'license_detail_text'):
            return

        selection = self.license_tree.selection()
        if not selection:
            return

        item = selection[0]
        values = self.license_tree.item(item)['values']

        if not values or values[0] == "无许可证":
            return

        model_name = values[0]

        # 查找对应的许可证数据
        license_data = None
        if self.model_licenses:
            for model_id, data in self.model_licenses.items():
                if data and data.get("model_name", model_id) == model_name:
                    license_data = data
                    break

        if license_data:
            # 显示详细信息
            detail_text = f"""许可证详细信息:

模型名称: {license_data.get('model_name', '未知')}
模型ID: {license_data.get('model_id', '未知')}
许可证ID: {license_data.get('license_id', '未知')}
创建时间: {license_data.get('created_at', '未知')}
过期时间: {license_data.get('expire_date', '永久')}
最大使用次数: {license_data.get('max_uses', '无限制')}
当前使用次数: {license_data.get('current_uses', 0)}
允许的机器: {', '.join(license_data.get('allowed_machines', []))}
状态: {'激活' if license_data.get('is_active', False) else '未激活'}"""

            self.license_detail_text.config(state=tk.NORMAL)
            self.license_detail_text.delete(1.0, tk.END)
            self.license_detail_text.insert(1.0, detail_text)
            self.license_detail_text.config(state=tk.DISABLED)

    def export_machine_id(self):
        """导出机器码"""
        from tkinter import filedialog, messagebox

        file_path = filedialog.asksaveasfilename(
            title="保存机器码文件",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialfile="machine_id.txt"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"机器码: {self.machine_id}\n")
                    f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"计算机名: {platform.node()}\n")

                messagebox.showinfo("导出成功", f"机器码已保存到: {file_path}")

            except Exception as e:
                messagebox.showerror("错误", f"保存机器码失败: {e}")

        # 进度条
        self.progress = ttk.Progressbar(right_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=(10, 0))

        # 参数控件字典
        self.param_widgets = {}

    def create_log_tab(self, notebook):
        """创建运行日志选项卡"""
        log_frame = ttk.Frame(notebook)
        notebook.add(log_frame, text="运行日志")

        # 日志控制栏
        control_frame = ttk.Frame(log_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(control_frame, text="清除日志",
                  command=self.clear_log).pack(side=tk.LEFT)

        ttk.Button(control_frame, text="保存日志",
                  command=self.save_log).pack(side=tk.LEFT, padx=(5, 0))

        # 日志显示区域
        log_main_frame = ttk.Frame(log_frame)
        log_main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))

        self.log_text = tk.Text(log_main_frame, wrap=tk.WORD, font=("Consolas", 9))
        log_scrollbar = ttk.Scrollbar(log_main_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side="left", fill="both", expand=True)
        log_scrollbar.pack(side="right", fill="y")

        # 添加初始日志
        self.log(f"客户端启动成功 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.log(f"机器码: {self.machine_id}")

        if self.license_data:
            self.log(f"许可证: {self.license_data.get('client_name', '未知')}")
        else:
            self.log("许可证: 未导入")

    def create_info_tab(self, notebook):
        """创建系统信息选项卡"""
        info_frame = ttk.Frame(notebook)
        notebook.add(info_frame, text="系统信息")

        info_main_frame = ttk.Frame(info_frame, padding=20)
        info_main_frame.pack(fill=tk.BOTH, expand=True)

        # 系统信息
        system_frame = ttk.LabelFrame(info_main_frame, text="系统信息", padding=15)
        system_frame.pack(fill=tk.X, pady=(0, 15))

        system_info = f"""机器名: {platform.node()}
操作系统: {platform.system()} {platform.release()}
处理器: {platform.processor()}
架构: {platform.machine()}
机器码: {self.machine_id}"""

        ttk.Label(system_frame, text=system_info, font=("Consolas", 9)).pack(anchor=tk.W)

        # 许可证信息
        license_frame = ttk.LabelFrame(info_main_frame, text="许可证信息", padding=15)
        license_frame.pack(fill=tk.X, pady=(0, 15))

        if self.license_data:
            license_info = f"""许可证ID: {self.license_data.get('license_id', '未知')}
客户端名称: {self.license_data.get('client_name', '未知')}
创建时间: {self.license_data.get('created_at', '未知')}
过期时间: {self.license_data.get('expire_date', '无限制')}
最大使用次数: {self.license_data.get('max_uses', '无限制')}
当前使用次数: {self.license_data.get('current_uses', 0)}"""
        else:
            license_info = """许可证状态: 未导入许可证
请导入许可证文件以激活模型使用功能"""

        ttk.Label(license_frame, text=license_info, font=("Consolas", 9)).pack(anchor=tk.W)

        # 操作按钮
        action_frame = ttk.Frame(info_main_frame)
        action_frame.pack(fill=tk.X, pady=(15, 0))

        ttk.Button(action_frame, text="重置许可证",
                  command=self.reregister).pack(side=tk.LEFT)

        ttk.Button(action_frame, text="关于",
                  command=self.show_about).pack(side=tk.RIGHT)

    def run_selected_model(self):
        """运行选中的模型"""
        selection = self.model_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个模型")
            return

        item = selection[0]
        model_name = self.model_tree.item(item)['text']

        # 检查许可证状态
        status = self.model_tree.item(item)['values'][0]
        if status != "已授权":
            messagebox.showerror("许可证错误", f"模型 '{model_name}' 未授权或许可证已过期，无法运行。\n\n请导入有效的许可证文件。")
            return

        # 验证参数
        input_file = self.input_file_var.get().strip()
        if not input_file:
            messagebox.showwarning("警告", "请选择输入文件")
            return

        if not os.path.exists(input_file):
            messagebox.showerror("错误", "输入文件不存在")
            return

        output_dir = self.output_dir_var.get().strip()
        if not output_dir:
            messagebox.showwarning("警告", "请选择输出目录")
            return

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 更新许可证使用次数
        self.update_license_usage(model_name)

        # 运行模型
        self.run_fme_model(model_name, input_file, output_dir)

    def run_fme_model(self, model_name, input_file, output_dir):
        """运行FME模型"""
        try:
            # 获取FME可执行文件路径
            fme_path = self.get_fme_path()

            if not fme_path or not os.path.exists(fme_path):
                self.log("错误: 未找到FME可执行文件")
                result = messagebox.askyesno("错误",
                    "未找到FME可执行文件，请确保FME已正确安装。\n\n是否要设置FME路径？")
                if result:
                    self.set_fme_path()
                return

            # 查找模型文件
            models_dir = get_resource_path("models")
            model_file = None

            for ext in ['.fmw', '.fmw.encrypted']:
                potential_file = os.path.join(models_dir, model_name + ext)
                if os.path.exists(potential_file):
                    model_file = potential_file
                    break

            if not model_file:
                messagebox.showerror("错误", f"找不到模型文件: {model_name}")
                return

            # 如果是加密文件，需要先解密
            if model_file.endswith('.encrypted'):
                self.log("检测到加密模型，正在解密...")
                decrypted_file = self.decrypt_model_file(model_file)
                if decrypted_file:
                    model_file = decrypted_file
                else:
                    messagebox.showerror("错误", "模型解密失败")
                    return

            # 构建命令
            cmd = [fme_path, model_file]

            # 添加参数
            for param_name, param_var in self.param_widgets.items():
                if hasattr(param_var, 'get'):
                    value = param_var.get()
                    if value:
                        cmd.extend([f"--{param_name}", value])

            self.log(f"开始运行模型: {model_name}")
            self.log(f"输入文件: {input_file}")
            self.log(f"输出目录: {output_dir}")
            self.log(f"执行命令: {' '.join(cmd)}")

            self.progress.start()
            self.run_button.config(state=tk.DISABLED)

            # 在新线程中运行
            def run_process():
                try:
                    result = subprocess.run(cmd, capture_output=True, text=True,
                                          encoding='utf-8', timeout=300)  # 5分钟超时

                    self.root.after(0, lambda: self.on_process_complete(result, output_dir))

                except subprocess.TimeoutExpired:
                    self.root.after(0, lambda: self.on_process_error("运行超时（5分钟）"))
                except Exception as e:
                    self.root.after(0, lambda: self.on_process_error(str(e)))

            thread = threading.Thread(target=run_process)
            thread.daemon = True
            thread.start()

        except Exception as e:
            self.log(f"运行模型失败: {e}")
            messagebox.showerror("错误", f"运行模型失败: {e}")

    def decrypt_model_file(self, encrypted_file):
        """解密模型文件"""
        try:
            # 简单的解密逻辑（实际应用中应使用更安全的方法）
            with open(encrypted_file, 'rb') as f:
                encrypted_data = f.read()

            # 使用固定密钥解密（与主程序保持一致）
            key = b"FME_ENCRYPTION_KEY_2024_SECURE"
            decrypted_data = bytes(a ^ b for a, b in zip(encrypted_data, key * (len(encrypted_data) // len(key) + 1)))

            # 保存到临时文件
            temp_file = encrypted_file.replace('.encrypted', '_temp.fmw')
            with open(temp_file, 'wb') as f:
                f.write(decrypted_data)

            return temp_file

        except Exception as e:
            self.log(f"解密模型文件失败: {e}")
            return None

    def on_process_complete(self, result, output_dir):
        """进程完成回调"""
        self.progress.stop()
        self.run_button.config(state=tk.NORMAL)

        if result.returncode == 0:
            self.log("模型运行完成")
            if result.stdout:
                self.log(f"输出: {result.stdout}")

            if messagebox.askyesno("完成", "模型运行完成！是否打开输出目录？"):
                try:
                    if platform.system() == "Windows":
                        os.startfile(output_dir)
                    else:
                        subprocess.run(["open", output_dir])  # macOS
                except:
                    pass
        else:
            self.log(f"模型运行失败，返回码: {result.returncode}")
            if result.stderr:
                self.log(f"错误: {result.stderr}")
            messagebox.showerror("错误", f"模型运行失败:\n{result.stderr}")

    def on_process_error(self, error):
        """进程错误回调"""
        self.progress.stop()
        self.run_button.config(state=tk.NORMAL)
        self.log(f"运行出错: {error}")
        messagebox.showerror("错误", f"运行出错: {error}")

    def reset_parameters(self):
        """重置参数"""
        self.input_file_var.set("")
        self.output_dir_var.set(os.path.join(os.getcwd(), "output"))

    def set_fme_path(self):
        """设置FME路径"""
        from tkinter import filedialog, messagebox

        # 获取当前FME路径
        current_path = self.get_fme_path()

        file_path = filedialog.askopenfilename(
            title="选择FME可执行文件",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")],
            initialdir=os.path.dirname(current_path) if current_path else "C:\\Program Files"
        )

        if file_path:
            try:
                # 保存FME路径到配置文件
                config_file = get_resource_path("fme_config.json")
                config = {}
                if os.path.exists(config_file):
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)

                config['fme_path'] = file_path

                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)

                messagebox.showinfo("成功", f"FME路径已设置为:\n{file_path}")

            except Exception as e:
                messagebox.showerror("错误", f"保存FME路径失败: {e}")

    def get_fme_path(self):
        """获取FME路径"""
        try:
            config_file = get_resource_path("fme_config.json")
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                return config.get('fme_path', '')
        except:
            pass

        # 默认FME路径
        default_paths = [
            "C:\\Program Files\\FME\\fme.exe",
            "C:\\Program Files (x86)\\FME\\fme.exe"
        ]

        for path in default_paths:
            if os.path.exists(path):
                return path

        return ""

    def update_license_usage(self, model_name):
        """更新许可证使用次数"""
        if not self.model_licenses:
            return

        # 查找对应的模型许可证
        model_id = None
        for mid, license_data in self.model_licenses.items():
            if license_data.get("model_name") == model_name:
                model_id = mid
                break

        if model_id and model_id in self.model_licenses:
            license_data = self.model_licenses[model_id]
            if "max_uses" in license_data:
                license_data["current_uses"] = license_data.get("current_uses", 0) + 1

                # 保存更新后的许可证到exe同目录
                try:
                    from encryption import LicenseManager
                    license_manager = LicenseManager()
                    exe_dir = os.path.dirname(sys.executable if getattr(sys, 'frozen', False) else __file__)
                    local_license_file = os.path.join(exe_dir, "license.lic")
                    license_manager.save_multi_model_license(self.model_licenses, local_license_file)

                    print(f"模型 {model_name} 使用次数已更新: {license_data['current_uses']}")

                    # 刷新显示
                    self.refresh_license_display()
                    self.load_available_models()

                except Exception as e:
                    print(f"更新许可证使用次数失败: {e}")

    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def clear_log(self):
        """清除日志"""
        if messagebox.askyesno("确认", "确定要清除所有日志吗？"):
            self.log_text.delete(1.0, tk.END)

    def save_log(self):
        """保存日志"""
        file_path = filedialog.asksaveasfilename(
            title="保存日志",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialvalue=f"fme_client_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("成功", f"日志已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存日志失败: {e}")

    def reregister(self):
        """重置许可证"""
        if messagebox.askyesno("确认", "确定要重置许可证吗？这将清除当前的许可证信息。"):
            try:
                # 清除许可证文件（检查exe同目录和当前目录）
                exe_dir = os.path.dirname(sys.executable if getattr(sys, 'frozen', False) else __file__)
                license_files = ["license.dat", "license.lic"]

                for license_file in license_files:
                    # 检查exe同目录
                    exe_license_path = os.path.join(exe_dir, license_file)
                    if os.path.exists(exe_license_path):
                        os.remove(exe_license_path)
                        print(f"已删除许可证文件: {exe_license_path}")

                    # 检查当前目录
                    if os.path.exists(license_file):
                        os.remove(license_file)
                        print(f"已删除许可证文件: {license_file}")

                # 重置许可证数据
                self.license_data = None
                self.model_licenses = {}
                self.is_registered = False

                # 更新界面状态
                self.update_license_status()
                self.load_available_models()
                self.refresh_license_display()

                messagebox.showinfo("成功", "许可证已重置，请重新导入许可证文件。")

            except Exception as e:
                messagebox.showerror("错误", f"重置许可证失败: {e}")

    def show_about(self):
        """显示关于信息"""
        about_text = f"""FME模型运行客户端 v1.0

客户端信息:
• 许可证: {self.license_data.get('client_name', '未知')}
• 机器码: {self.machine_id}
• 创建时间: {self.license_data.get('created_at', '未知')}

功能特性:
• 自动机器码检测
• 注册码激活
• 模型运行管理
• 运行日志记录
• 加密模型支持

技术支持:
如有问题请联系管理员
"""
        messagebox.showinfo("关于", about_text)

    def run(self):
        """运行客户端"""
        self.root.mainloop()

if __name__ == "__main__":
    try:
        client = FMEClient()
        client.run()
    except Exception as e:
        messagebox.showerror("错误", f"客户端启动失败: {e}")
        sys.exit(1)
    
    def get_machine_id(self):
        """获取机器唯一标识"""
        machine_info = f"{platform.node()}-{platform.machine()}-{platform.processor()}"
        return hashlib.md5(machine_info.encode()).hexdigest()
    
    def validate_license(self):
        """验证许可证"""
        if not self.license or not self.license.get("is_active"):
            return False
        
        # 检查过期时间
        if self.license.get("expire_date"):
            try:
                expire_date = datetime.fromisoformat(self.license["expire_date"])
                if datetime.now() > expire_date:
                    messagebox.showerror("许可证过期", "许可证已过期")
                    return False
            except:
                return False
        
        # 检查使用次数
        max_uses = self.license.get("max_uses")
        if max_uses and self.license.get("current_uses", 0) >= max_uses:
            messagebox.showerror("使用次数超限", "许可证使用次数已达上限")
            return False
        
        # 检查机器码
        allowed_machines = self.license.get("allowed_machines", [])
        if allowed_machines:
            machine_id = self.get_machine_id()
            if machine_id not in allowed_machines:
                messagebox.showerror("机器码不匹配", "此机器无权使用该客户端")
                return False
        
        return True
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="FME模型运行客户端", 
                               font=("", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 模型选择
        ttk.Label(main_frame, text="选择模型:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.model_var = tk.StringVar()
        self.model_combo = ttk.Combobox(main_frame, textvariable=self.model_var, 
                                       width=50, state="readonly")
        self.model_combo.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        self.model_combo.bind('<<ComboboxSelected>>', self.on_model_select)
        
        # 参数设置区域
        params_label = ttk.Label(main_frame, text="参数设置:")
        params_label.grid(row=2, column=0, sticky=(tk.W, tk.N), pady=5)
        
        # 参数框架（带滚动条）
        self.params_canvas = tk.Canvas(main_frame, height=200)
        params_scrollbar = ttk.Scrollbar(main_frame, orient="vertical", 
                                        command=self.params_canvas.yview)
        self.params_scrollable_frame = ttk.Frame(self.params_canvas)
        
        self.params_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.params_canvas.configure(scrollregion=self.params_canvas.bbox("all"))
        )
        
        self.params_canvas.create_window((0, 0), window=self.params_scrollable_frame, anchor="nw")
        self.params_canvas.configure(yscrollcommand=params_scrollbar.set)
        
        self.params_canvas.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        params_scrollbar.grid(row=3, column=2, sticky=(tk.N, tk.S), pady=5)
        
        # 运行按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=10)
        
        self.run_button = ttk.Button(button_frame, text="运行模型", command=self.run_model)
        self.run_button.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="重置参数", command=self.reset_parameters).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关于", command=self.show_about).pack(side=tk.RIGHT, padx=5)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding=5)
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = tk.Text(log_frame, height=10, width=80)
        log_scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 参数输入控件字典
        self.param_widgets = {}
    
    def load_models(self):
        """加载可用模型"""
        models = []
        self.model_files = {}
        
        models_info = self.config.get("models", [])
        for model_info in models_info:
            model_name = model_info.get("name", "")
            file_name = model_info.get("file_name", "")
            
            if os.path.exists(file_name):
                models.append(model_name)
                self.model_files[model_name] = file_name
        
        self.model_combo['values'] = models
        if models:
            self.model_combo.set(models[0])
            self.on_model_select()
    
    def on_model_select(self, event=None):
        """模型选择事件"""
        model_name = self.model_var.get()
        if not model_name:
            return
        
        # 清空现有参数控件
        for widget in self.params_scrollable_frame.winfo_children():
            widget.destroy()
        
        self.param_widgets.clear()
        
        # 这里应该解析FMW文件获取参数，简化版本只显示基本参数
        self.create_basic_parameters()
    
    def create_basic_parameters(self):
        """创建基本参数输入"""
        row = 0
        
        # 输入文件参数
        ttk.Label(self.params_scrollable_frame, text="输入文件:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        
        input_frame = ttk.Frame(self.params_scrollable_frame)
        input_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        
        self.input_file_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.input_file_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        def browse_input():
            file_path = filedialog.askopenfilename(title="选择输入文件")
            if file_path:
                self.input_file_var.set(file_path)
        
        ttk.Button(input_frame, text="浏览", command=browse_input).pack(side=tk.RIGHT, padx=(5, 0))
        
        row += 1
        
        # 输出目录参数
        ttk.Label(self.params_scrollable_frame, text="输出目录:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        
        output_frame = ttk.Frame(self.params_scrollable_frame)
        output_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        
        self.output_dir_var = tk.StringVar(value=os.path.join(os.getcwd(), "output"))
        ttk.Entry(output_frame, textvariable=self.output_dir_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        def browse_output():
            dir_path = filedialog.askdirectory(title="选择输出目录")
            if dir_path:
                self.output_dir_var.set(dir_path)
        
        ttk.Button(output_frame, text="浏览", command=browse_output).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 配置列权重
        self.params_scrollable_frame.columnconfigure(1, weight=1)
    
    def reset_parameters(self):
        """重置参数"""
        self.input_file_var.set("")
        self.output_dir_var.set(os.path.join(os.getcwd(), "output"))
    
    def run_model(self):
        """运行模型"""
        model_name = self.model_var.get()
        if not model_name:
            messagebox.showwarning("警告", "请选择一个模型")
            return
        
        input_file = self.input_file_var.get().strip()
        if not input_file:
            messagebox.showwarning("警告", "请选择输入文件")
            return
        
        if not os.path.exists(input_file):
            messagebox.showerror("错误", "输入文件不存在")
            return
        
        output_dir = self.output_dir_var.get().strip()
        if not output_dir:
            messagebox.showwarning("警告", "请选择输出目录")
            return
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 更新许可证使用次数
        self.update_license_usage()
        
        # 运行FME
        self.run_fme_model(model_name, input_file, output_dir)
    
    def run_fme_model(self, model_name, input_file, output_dir):
        """运行FME模型"""
        try:
            fme_path = self.config.get("fme_path", "fme.exe")
            model_file = self.model_files.get(model_name)
            
            if not model_file:
                messagebox.showerror("错误", "找不到模型文件")
                return
            
            # 构建命令
            cmd = [fme_path, model_file, f"--SourceDataset={input_file}", f"--DestDataset={output_dir}"]
            
            self.log(f"开始运行模型: {model_name}")
            self.log(f"输入文件: {input_file}")
            self.log(f"输出目录: {output_dir}")
            self.log(f"执行命令: {' '.join(cmd)}")
            
            self.progress.start()
            self.run_button.config(state=tk.DISABLED)
            
            # 在新线程中运行
            import threading
            
            def run_process():
                try:
                    result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
                    
                    self.root.after(0, lambda: self.on_process_complete(result, output_dir))
                    
                except Exception as e:
                    self.root.after(0, lambda: self.on_process_error(str(e)))
            
            thread = threading.Thread(target=run_process)
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            self.log(f"运行模型失败: {e}")
            messagebox.showerror("错误", f"运行模型失败: {e}")
    
    def on_process_complete(self, result, output_dir):
        """进程完成回调"""
        self.progress.stop()
        self.run_button.config(state=tk.NORMAL)
        
        if result.returncode == 0:
            self.log("模型运行完成")
            if result.stdout:
                self.log(f"输出: {result.stdout}")
            
            if messagebox.askyesno("完成", "模型运行完成！是否打开输出目录？"):
                try:
                    os.startfile(output_dir)
                except:
                    pass
        else:
            self.log(f"模型运行失败，返回码: {result.returncode}")
            if result.stderr:
                self.log(f"错误: {result.stderr}")
            messagebox.showerror("错误", f"模型运行失败:\n{result.stderr}")
    
    def on_process_error(self, error):
        """进程错误回调"""
        self.progress.stop()
        self.run_button.config(state=tk.NORMAL)
        self.log(f"运行出错: {error}")
        messagebox.showerror("错误", f"运行出错: {error}")
    
    def update_license_usage(self):
        """更新许可证使用次数"""
        if self.license and "max_uses" in self.license:
            self.license["current_uses"] = self.license.get("current_uses", 0) + 1
            
            try:
                # 重新加密并保存许可证
                from cryptography.fernet import Fernet
                
                # 读取原始许可证文件获取密钥
                with open("license.json", "rb") as f:
                    data = base64.b64decode(f.read())
                
                key = data[:44]
                fernet = Fernet(key)
                
                # 加密更新后的许可证
                license_json = json.dumps(self.license, ensure_ascii=False)
                encrypted_license = fernet.encrypt(license_json.encode())
                
                # 保存
                with open("license.json", "wb") as f:
                    f.write(base64.b64encode(key + encrypted_license))
                    
            except Exception as e:
                self.log(f"更新许可证失败: {e}")
    
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def show_about(self):
        """显示关于信息"""
        # 安全获取配置信息
        config = getattr(self, 'config', {}) or {}
        license_data = getattr(self, 'license_data', None)

        about_text = f"""FME模型运行客户端

客户端名称: {config.get('client_name', '未知')}
创建时间: {config.get('created_at', '未知')}
包含模型: {len(config.get('models', []))}个

许可证信息:"""

        if license_data:
            about_text += f"""
- 许可证ID: {license_data.get('license_id', '未知')}
- 过期时间: {license_data.get('expire_date', '无限制')}
- 使用次数: {license_data.get('current_uses', 0)}/{license_data.get('max_uses', '无限制')}"""
        else:
            about_text += """
- 状态: 未导入许可证"""
        messagebox.showinfo("关于", about_text)
    
    def run(self):
        """运行客户端"""
        self.root.mainloop()

if __name__ == "__main__":
    try:
        client = FMEClient()
        client.run()
    except Exception as e:
        messagebox.showerror("错误", f"客户端启动失败: {e}")
        sys.exit(1)
