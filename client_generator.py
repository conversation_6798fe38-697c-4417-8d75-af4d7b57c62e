"""
分发客户端生成模块
"""
import os
import sys
import json
import shutil
import zipfile
import tempfile
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
from config import config
from encryption import LicenseManager
from model_manager import ModelManager
from database import DatabaseManager

class ClientGenerator:
    """分发客户端生成器"""
    
    def __init__(self):
        self.clients_dir = config.get("clients_dir")
        self.model_manager = ModelManager()
        self.license_manager = LicenseManager()
        self.db = DatabaseManager()
        self.history_file = os.path.join(self.clients_dir, "client_history.json")

        # 确保客户端目录存在
        os.makedirs(self.clients_dir, exist_ok=True)
    
    def create_client_template(self, client_name="FME模型运行客户端"):
        """创建客户端模板"""
        # 读取客户端模板文件
        template_file = "client_template.py"
        if os.path.exists(template_file):
            with open(template_file, 'r', encoding='utf-8') as f:
                template_content = f.read()

            # 替换模板中的应用名称
            template_content = self.customize_client_template(template_content, client_name)
            return template_content
        else:
            # 如果模板文件不存在，返回简化版本
            return f'''"""
{client_name}
"""
import os
import sys
import json
import tkinter as tk
from tkinter import ttk, messagebox

class FMEClient:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("{client_name}")
        self.root.geometry("600x400")

        ttk.Label(self.root, text="FME模型运行客户端",
                 font=("", 16, "bold")).pack(pady=20)
        ttk.Label(self.root, text="客户端模板文件缺失，请联系管理员").pack(pady=10)

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    client = FMEClient()
    client.run()
'''

    def customize_client_template(self, template_content, client_name):
        """自定义客户端模板，替换应用名称"""
        # 替换所有出现的"FME模型运行客户端"
        customized_content = template_content.replace("FME模型运行客户端", client_name)

        # 确保文档字符串也被替换
        if '"""' in customized_content and "FME模型运行客户端" in customized_content:
            # 如果还有遗漏的，再次替换
            customized_content = customized_content.replace("FME模型运行客户端", client_name)

        return customized_content

    def copy_encryption_module(self, client_dir):
        """复制加密模块到客户端目录"""
        try:
            # 复制encryption.py文件
            encryption_source = "encryption.py"
            if os.path.exists(encryption_source):
                encryption_dest = os.path.join(client_dir, "encryption.py")
                shutil.copy2(encryption_source, encryption_dest)
                print(f"✓ 加密模块已复制到客户端")
            else:
                print(f"⚠️ 加密模块文件不存在: {encryption_source}")
                # 创建简化的加密模块
                self.create_simplified_encryption_module(client_dir)
        except Exception as e:
            print(f"✗ 复制加密模块失败: {e}")
            # 创建简化的加密模块作为备用
            self.create_simplified_encryption_module(client_dir)

    def create_simplified_encryption_module(self, client_dir):
        """创建简化的加密模块"""
        encryption_content = '''"""
简化的加密模块 - 客户端版本
"""
import os
import json
import base64
from cryptography.fernet import Fernet
from datetime import datetime
import hashlib
import platform


class LicenseManager:
    """许可证管理器"""

    def __init__(self):
        self.key = self._get_or_create_key()
        self.cipher = Fernet(self.key)

    def _get_or_create_key(self):
        """获取或创建加密密钥"""
        # 使用固定的密钥（在实际应用中应该更安全）
        key_string = "FME_CLIENT_LICENSE_KEY_2024"
        key_bytes = hashlib.sha256(key_string.encode()).digest()
        return base64.urlsafe_b64encode(key_bytes)

    def load_multi_model_license(self, license_file):
        """加载多模型许可证文件"""
        try:
            if not os.path.exists(license_file):
                print(f"许可证文件不存在: {license_file}")
                return {}

            with open(license_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            # 解析许可证文件
            licenses = {}
            for line in content.split('\\n'):
                if ':' in line:
                    try:
                        model_id, encrypted_data = line.split(':', 1)
                        # 解密许可证数据
                        decrypted_data = self.cipher.decrypt(base64.b64decode(encrypted_data))
                        license_data = json.loads(decrypted_data.decode('utf-8'))
                        licenses[model_id] = license_data
                    except Exception as e:
                        print(f"解析模型 {model_id} 的许可证失败: {e}")
                        continue

            print(f"多模型许可证加载成功: {license_file}, 共加载 {len(licenses)} 个模型许可证")
            return licenses

        except Exception as e:
            print(f"加载多模型许可证失败: {e}")
            return {}

    def save_multi_model_license(self, model_licenses, license_file):
        """保存多模型许可证文件"""
        try:
            lines = []
            for model_id, license_data in model_licenses.items():
                # 加密许可证数据
                json_data = json.dumps(license_data, ensure_ascii=False)
                encrypted_data = self.cipher.encrypt(json_data.encode('utf-8'))
                encoded_data = base64.b64encode(encrypted_data).decode('utf-8')
                lines.append(f"{model_id}:{encoded_data}")

            with open(license_file, 'w', encoding='utf-8') as f:
                f.write('\\n'.join(lines))

            print(f"多模型许可证保存成功: {license_file}")
            return True

        except Exception as e:
            print(f"保存多模型许可证失败: {e}")
            return False

    def validate_license(self, license_data):
        """验证许可证"""
        try:
            # 检查过期时间
            if license_data.get("expire_date"):
                expire_date = datetime.fromisoformat(license_data["expire_date"])
                if datetime.now() > expire_date:
                    return False, "许可证已过期"

            # 检查使用次数
            max_uses = license_data.get("max_uses")
            if max_uses:
                current_uses = license_data.get("current_uses", 0)
                if current_uses >= max_uses:
                    return False, "使用次数已达上限"

            # 检查机器码
            allowed_machines = license_data.get("allowed_machines", [])
            if allowed_machines:
                machine_id = self._get_machine_id()
                if machine_id not in allowed_machines:
                    return False, "机器码不匹配"

            return True, "许可证有效"

        except Exception as e:
            return False, f"验证失败: {e}"

    def _get_machine_id(self):
        """获取机器码"""
        try:
            # 获取机器的唯一标识
            machine_info = f"{platform.node()}-{platform.machine()}-{platform.processor()}"
            return hashlib.md5(machine_info.encode()).hexdigest()
        except:
            return "unknown_machine"
'''

        try:
            encryption_file = os.path.join(client_dir, "encryption.py")
            with open(encryption_file, 'w', encoding='utf-8') as f:
                f.write(encryption_content)
            print(f"✓ 简化加密模块已创建")
        except Exception as e:
            print(f"✗ 创建简化加密模块失败: {e}")
    
    def generate_client(self, selected_models, client_info):
        """生成分发客户端"""
        try:
            client_name = client_info['name']

            # 创建客户端目录
            client_id = f"client_{int(datetime.now().timestamp())}"
            client_dir = os.path.join(self.clients_dir, client_id)
            os.makedirs(client_dir, exist_ok=True)

            # 创建模型目录
            models_dir = os.path.join(client_dir, "models")
            os.makedirs(models_dir, exist_ok=True)

            # 复制选中的模型
            copied_models = []
            failed_models = []

            for model_id in selected_models:
                model_info = self.model_manager.get_model_info(model_id)
                if not model_info:
                    failed_models.append(f"模型ID {model_id}: 无法获取模型信息")
                    continue

                # 获取源文件路径（使用绝对路径）
                try:
                    source_file = self.model_manager.get_absolute_path(model_info["file_path"])
                    # 标准化路径，确保路径分隔符正确
                    source_file = os.path.normpath(source_file)
                except Exception as e:
                    failed_models.append(f"模型 {model_info.get('name', model_id)}: 无法获取文件路径 - {e}")
                    continue

                # 确定目标文件名
                if model_info.get("is_encrypted", False):
                    # 使用隐藏文件名
                    dest_filename = model_info.get("hidden_filename", f"model_{model_id}.dat")
                    if not dest_filename.endswith('.enc'):
                        dest_filename += ".enc"
                else:
                    # 使用原始文件名
                    dest_filename = model_info.get("original_filename", f"{model_info['name']}.fmw")

                dest_file = os.path.join(models_dir, dest_filename)

                # 复制文件
                if not os.path.exists(source_file):
                    # 尝试查找可能的替代文件
                    model_dir = os.path.dirname(source_file)
                    if os.path.exists(model_dir):
                        files = [f for f in os.listdir(model_dir) if f.endswith(('.enc', '.fmw', '.dat'))]
                        if len(files) == 1:
                            # 找到唯一的模型文件，使用它
                            alternative_file = os.path.join(model_dir, files[0])
                            print(f"警告：使用替代文件 {alternative_file} 代替 {source_file}")
                            source_file = alternative_file
                        else:
                            failed_models.append(f"模型 {model_info.get('name', model_id)}: 源文件不存在 - {source_file}")
                            continue
                    else:
                        failed_models.append(f"模型 {model_info.get('name', model_id)}: 源文件不存在 - {source_file}")
                        continue

                try:
                    shutil.copy2(source_file, dest_file)
                    copied_models.append({
                        "id": model_id,
                        "name": model_info["name"],
                        "file_name": dest_filename,
                        "is_encrypted": model_info.get("is_encrypted", False),
                        "original_filename": model_info.get("original_filename", f"{model_info['name']}.fmw"),
                        "description": model_info.get("description", ""),
                        "category": model_info.get("category", "默认")
                    })
                except Exception as e:
                    failed_models.append(f"模型 {model_info.get('name', model_id)}: 复制失败 - {e}")

            # 检查复制结果
            if not copied_models:
                error_msg = "没有成功复制任何模型文件"
                if failed_models:
                    error_msg += ":\n" + "\n".join(failed_models)
                return False, error_msg, None

            # 如果有部分失败，记录警告
            if failed_models:
                print(f"警告：部分模型复制失败:\n" + "\n".join(failed_models))

            # 创建客户端配置
            client_config = {
                "client_name": client_name,
                "client_id": client_id,
                "project": client_info.get('project', ''),
                "user": client_info.get('user', ''),
                "contact": client_info.get('contact', ''),
                "description": client_info.get('description', ''),
                "created_at": datetime.now().isoformat(),
                "models": copied_models,
                "fme_path": config.get("fme_path")
            }

            config_file = os.path.join(client_dir, "client_config.json")
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(client_config, f, ensure_ascii=False, indent=2)

            # 创建客户端主程序
            client_script = os.path.join(client_dir, "fme_client.py")
            with open(client_script, 'w', encoding='utf-8') as f:
                f.write(self.create_client_template(client_name))

            # 复制加密模块
            self.copy_encryption_module(client_dir)

            # 创建启动脚本
            start_script = os.path.join(client_dir, "start.bat")
            with open(start_script, 'w', encoding='utf-8') as f:
                f.write(f'@echo off\ncd /d "%~dp0"\npython fme_client.py\npause')

            # 创建README文件
            readme_file = os.path.join(client_dir, "README.txt")
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(f"""FME模型运行客户端
客户端名称: {client_name}
使用项目: {client_info.get('project', '')}
使用人: {client_info.get('user', '')}
联系方式: {client_info.get('contact', '')}
描述: {client_info.get('description', '')}
创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
包含模型: {len(copied_models)}个

模型列表:
""")
                for model in copied_models:
                    f.write(f"- {model['name']} ({model['category']})\n")

                f.write(f"""
使用说明:
1. 确保已安装Python 3.7+和FME
2. 双击start.bat启动客户端
3. 或者在命令行中运行: python fme_client.py

注意事项:
- 所有模型都已加密保护
- 需要有效的许可证才能运行模型
- 请联系管理员获取许可证文件
""")

            # 完整的打包流程（包含进度条）
            success, message, zip_path = self.build_complete_client_package(
                client_dir, client_name, client_id, len(copied_models), client_info
            )

            return success, message, zip_path

        except Exception as e:
            # 保存失败记录
            self.save_client_history(client_info, 0, f"失败: {e}")
            return False, f"客户端生成失败: {e}", None

    def save_client_history(self, client_info, model_count, status):
        """保存客户端创建历史到数据库"""
        try:
            # 保存到数据库
            history_data = {
                "created_at": datetime.now().isoformat(),
                "client_name": client_info.get('name', ''),
                "project": client_info.get('project', ''),
                "user": client_info.get('user', ''),
                "contact": client_info.get('contact', ''),
                "description": client_info.get('description', ''),
                "model_count": model_count,
                "status": status
            }

            self.db.save_client_history(history_data)

            # 保持向后兼容，也保存到JSON
            history = []
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)

            # 添加新记录（使用字符串格式的时间）
            record = dict(history_data)
            record["created_at"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            history.append(record)

            # 保持最近100条记录
            if len(history) > 100:
                history = history[-100:]

            # 保存历史
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"保存历史记录失败: {e}")

    def get_client_history(self):
        """获取客户端创建历史"""
        try:
            # 优先从数据库获取
            db_history = self.db.get_client_history()
            if db_history:
                return db_history

            # 回退到JSON文件
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
                # 按创建时间倒序排列
                return sorted(history, key=lambda x: x.get('created_at', ''), reverse=True)
            else:
                return []
        except Exception as e:
            print(f"读取历史记录失败: {e}")
            return []

    def build_complete_client_package(self, client_dir, client_name, client_id, model_count, client_info):
        """完整的客户端打包流程（同步方式带进度条）"""
        try:
            print(f"开始打包客户端: {client_name}")

            # 检查是否在GUI环境中
            try:
                import tkinter as tk
                root = tk._default_root
                if root is None:
                    # 不在GUI环境中，直接执行打包
                    result = self._build_client_package_worker(client_dir, client_name, client_id, model_count, client_info)
                    if result['success']:
                        return True, result['message'], result['zip_path']
                    else:
                        return False, result['message'], None
            except:
                # 不在GUI环境中，直接执行打包
                result = self._build_client_package_worker(client_dir, client_name, client_id, model_count, client_info)
                if result['success']:
                    return True, result['message'], result['zip_path']
                else:
                    return False, result['message'], None

            # 在GUI环境中，显示同步进度条
            from dialogs import SyncProgressDialog

            # 创建进度对话框
            progress_dialog = SyncProgressDialog(None, f"正在打包客户端: {client_name}")

            try:
                # 执行打包并更新进度
                result = self._build_client_package_worker_with_progress(
                    client_dir, client_name, client_id, model_count, client_info, progress_dialog
                )

                if result['success']:
                    progress_dialog.complete("打包完成！")
                    print(f"客户端打包成功: {result['message']}")
                    return True, result['message'], result['zip_path']
                else:
                    progress_dialog.error(f"打包失败: {result['message']}")
                    print(f"客户端打包失败: {result['message']}")
                    return False, result['message'], None

            finally:
                # 确保对话框关闭
                progress_dialog.close()

        except Exception as e:
            error_msg = f"打包过程中发生错误: {e}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            return False, error_msg, None

    def build_client_exe_sync_with_progress(self, client_dir, client_name, client_id, progress_dialog):
        """同步打包exe（带进度）"""
        try:
            progress_dialog.update_progress(25, "检查PyInstaller环境...")

            # 检查PyInstaller
            try:
                import PyInstaller
            except ImportError:
                return False, "PyInstaller未安装，请先安装: pip install pyinstaller", None

            # 构建PyInstaller命令
            client_script = os.path.join(client_dir, "fme_client.py")
            if not os.path.exists(client_script):
                return False, f"客户端脚本不存在: {client_script}", None

            # 使用绝对路径
            client_script = os.path.abspath(client_script)
            client_dir = os.path.abspath(client_dir)

            # 输出目录
            dist_dir = os.path.join(client_dir, "dist")
            build_dir = os.path.join(client_dir, "build")

            # 确保目录存在
            os.makedirs(dist_dir, exist_ok=True)
            os.makedirs(build_dir, exist_ok=True)

            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--onefile",
                "--windowed",
                "--name", client_name,
                "--distpath", dist_dir,
                "--workpath", build_dir,
                "--specpath", client_dir,
                "--clean",
                "--noconfirm"  # 不询问覆盖
            ]

            # 添加客户端配置文件
            config_file = os.path.join(client_dir, "client_config.json")
            if os.path.exists(config_file):
                cmd.extend(["--add-data", f"{config_file};."])

            # 添加加密模块
            encryption_file = os.path.join(client_dir, "encryption.py")
            if os.path.exists(encryption_file):
                cmd.extend(["--add-data", f"{encryption_file};."])

            # 添加解析模块
            parse_fmw_file = os.path.join(client_dir, "parse_fmw.py")
            if os.path.exists(parse_fmw_file):
                cmd.extend(["--add-data", f"{parse_fmw_file};."])

            # 添加必要的Python模块
            cmd.extend([
                "--hidden-import", "tkinter",
                "--hidden-import", "ttkbootstrap",
                "--hidden-import", "json",
                "--hidden-import", "os",
                "--hidden-import", "subprocess",
                "--hidden-import", "threading"
            ])

            cmd.append(client_script)

            # 执行PyInstaller（在当前目录执行，避免路径问题）
            progress_dialog.update_progress(30, "正在执行PyInstaller...")
            print(f"执行命令: {' '.join(cmd)}")
            print(f"工作目录: {os.getcwd()}")
            print(f"客户端脚本: {client_script}")
            print(f"脚本存在: {os.path.exists(client_script)}")

            # 使用超时执行PyInstaller
            try:
                result = subprocess.run(cmd, capture_output=True, text=True,
                                      cwd=os.getcwd(), timeout=300)  # 5分钟超时
            except subprocess.TimeoutExpired:
                return False, "PyInstaller执行超时（超过5分钟），可能是环境问题", None

            if result.returncode == 0:
                exe_file = os.path.join(dist_dir, f"{client_name}.exe")
                if os.path.exists(exe_file):
                    return True, "exe打包成功", exe_file
                else:
                    return False, "exe文件未生成", None
            else:
                error_msg = result.stderr if result.stderr else result.stdout
                print(f"PyInstaller错误输出: {error_msg}")
                return False, f"PyInstaller执行失败: {error_msg[:200]}...", None

        except Exception as e:
            import traceback
            traceback.print_exc()
            return False, f"exe打包过程中发生错误: {e}", None

    def _build_client_package_worker(self, client_dir, client_name, client_id, model_count, client_info):
        """客户端打包工作函数（同步，无进度）"""
        try:
            print("正在准备打包环境...")

            # 步骤1: 尝试打包exe
            print("正在打包exe文件...")
            exe_success, exe_message, exe_path = self.build_client_exe_sync(client_dir, client_name, client_id)

            # 步骤2: 创建ZIP包
            print("正在创建ZIP包...")

            if exe_success:
                # 创建包含exe的ZIP包
                zip_file = os.path.join(self.clients_dir, f"{client_name}_{client_id}_exe.zip")
                with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    # 添加exe文件
                    if os.path.exists(exe_path):
                        zipf.write(exe_path, f"{client_name}.exe")

                    # 添加模型文件
                    models_dir = os.path.join(client_dir, "models")
                    if os.path.exists(models_dir):
                        for root, dirs, files in os.walk(models_dir):
                            for file in files:
                                file_path = os.path.join(root, file)
                                arc_name = os.path.relpath(file_path, client_dir)
                                zipf.write(file_path, arc_name)

                    # 添加README文件
                    readme_file = os.path.join(client_dir, "README.txt")
                    if os.path.exists(readme_file):
                        zipf.write(readme_file, "README.txt")

                message = f"客户端exe生成成功: {zip_file}"

                print("正在清理临时文件...")
                # 清理临时文件（exe成功时）
                self.cleanup_temp_files(client_dir, exe_path)
            else:
                # 如果exe打包失败，回退到Python源码包
                print(f"exe打包失败: {exe_message}，生成Python源码包")

                zip_file = os.path.join(self.clients_dir, f"{client_name}_{client_id}_python.zip")
                with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for root, dirs, files in os.walk(client_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arc_name = os.path.relpath(file_path, client_dir)
                            zipf.write(file_path, arc_name)

                message = f"客户端Python包生成成功: {zip_file} (注意：需要Python环境)"

                progress_dialog.update_progress(85, "正在清理临时文件...")
                # 清理临时文件（Python包时）
                self.cleanup_temp_files(client_dir, None)

            # 步骤3: 保存历史记录
            progress_dialog.update_progress(95, "正在保存历史记录...")
            self.save_client_history(client_info, model_count, "成功")

            progress_dialog.update_progress(100, "打包完成！")

            return {
                'success': True,
                'message': message,
                'zip_path': zip_file
            }

        except Exception as e:
            # 保存失败记录
            self.save_client_history(client_info, model_count, f"失败: {e}")
            return {
                'success': False,
                'message': f"打包失败: {e}",
                'zip_path': None
            }

    def _build_client_package_worker_with_progress(self, client_dir, client_name, client_id, model_count, client_info, progress_dialog):
        """客户端打包工作函数（同步，带进度）"""
        try:
            progress_dialog.update_progress(10, "正在准备打包环境...")

            # 步骤1: 尝试打包exe
            progress_dialog.update_progress(20, "正在打包exe文件...")
            exe_success, exe_message, exe_path = self.build_client_exe_sync_with_progress(client_dir, client_name, client_id, progress_dialog)

            # 步骤2: 创建ZIP包
            progress_dialog.update_progress(60, "正在创建ZIP包...")

            if exe_success:
                # 创建包含exe的ZIP包
                zip_file = os.path.join(self.clients_dir, f"{client_name}_{client_id}_exe.zip")
                with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    # 添加exe文件
                    if os.path.exists(exe_path):
                        zipf.write(exe_path, f"{client_name}.exe")

                    # 添加模型文件
                    models_dir = os.path.join(client_dir, "models")
                    if os.path.exists(models_dir):
                        for root, dirs, files in os.walk(models_dir):
                            for file in files:
                                file_path = os.path.join(root, file)
                                arc_name = os.path.relpath(file_path, client_dir)
                                zipf.write(file_path, arc_name)

                    # 添加README文件
                    readme_file = os.path.join(client_dir, "README.txt")
                    if os.path.exists(readme_file):
                        zipf.write(readme_file, "README.txt")

                message = f"客户端exe生成成功: {zip_file}"

                progress_dialog.update_progress(85, "正在清理临时文件...")
                # 清理临时文件（exe成功时）
                self.cleanup_temp_files(client_dir, exe_path)
            else:
                # 如果exe打包失败，回退到Python源码包
                progress_dialog.update_progress(60, "exe打包失败，生成Python源码包...")

                zip_file = os.path.join(self.clients_dir, f"{client_name}_{client_id}_python.zip")
                with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for root, dirs, files in os.walk(client_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arc_name = os.path.relpath(file_path, client_dir)
                            zipf.write(file_path, arc_name)

                message = f"客户端Python包生成成功: {zip_file} (注意：需要Python环境)"

                print("正在清理临时文件...")
                # 清理临时文件（Python包时）
                self.cleanup_temp_files(client_dir, None)

            # 步骤4: 保存历史记录
            print("正在保存历史记录...")
            self.save_client_history(client_info, model_count, "成功")

            print("打包完成！")

            return {
                'success': True,
                'message': message,
                'zip_path': zip_file
            }

        except Exception as e:
            # 保存失败记录
            self.save_client_history(client_info, model_count, f"失败: {e}")
            return {
                'success': False,
                'message': f"打包失败: {e}",
                'zip_path': None
            }

    def cleanup_temp_files(self, client_dir, exe_path=None):
        """清理临时文件，只保留ZIP包"""
        try:
            # 删除客户端目录
            if os.path.exists(client_dir):
                shutil.rmtree(client_dir)
                print(f"已删除临时目录: {client_dir}")

            # 删除exe文件（如果存在且不在客户端目录中）
            if exe_path and os.path.exists(exe_path) and not exe_path.startswith(client_dir):
                os.remove(exe_path)
                print(f"已删除临时exe文件: {exe_path}")

        except Exception as e:
            print(f"清理临时文件失败: {e}")

    def build_client_exe_sync(self, client_dir, client_name, client_id):
        """同步打包exe"""
        try:
            print("检查PyInstaller环境...")

            # 检查PyInstaller
            try:
                import PyInstaller
            except ImportError:
                return False, "PyInstaller未安装，请先安装: pip install pyinstaller", None

            # 构建PyInstaller命令
            client_script = os.path.join(client_dir, "fme_client.py")
            if not os.path.exists(client_script):
                return False, f"客户端脚本不存在: {client_script}", None

            # 使用绝对路径
            client_script = os.path.abspath(client_script)
            client_dir = os.path.abspath(client_dir)

            # 输出目录
            dist_dir = os.path.join(client_dir, "dist")
            build_dir = os.path.join(client_dir, "build")

            # 确保目录存在
            os.makedirs(dist_dir, exist_ok=True)
            os.makedirs(build_dir, exist_ok=True)

            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--onefile",
                "--windowed",
                "--name", client_name,
                "--distpath", dist_dir,
                "--workpath", build_dir,
                "--specpath", client_dir,
                "--clean",
                "--noconfirm"  # 不询问覆盖
            ]

            # 添加客户端配置文件
            config_file = os.path.join(client_dir, "client_config.json")
            if os.path.exists(config_file):
                cmd.extend(["--add-data", f"{config_file};."])

            # 添加加密模块
            encryption_file = os.path.join(client_dir, "encryption.py")
            if os.path.exists(encryption_file):
                cmd.extend(["--add-data", f"{encryption_file};."])

            # 添加解析模块
            parse_fmw_file = os.path.join(client_dir, "parse_fmw.py")
            if os.path.exists(parse_fmw_file):
                cmd.extend(["--add-data", f"{parse_fmw_file};."])

            # 添加必要的Python模块
            cmd.extend([
                "--hidden-import", "tkinter",
                "--hidden-import", "ttkbootstrap",
                "--hidden-import", "json",
                "--hidden-import", "os",
                "--hidden-import", "subprocess",
                "--hidden-import", "threading"
            ])

            cmd.append(client_script)

            # 执行PyInstaller（在当前目录执行，避免路径问题）
            print("正在执行PyInstaller...")
            print(f"执行命令: {' '.join(cmd)}")
            print(f"工作目录: {os.getcwd()}")
            print(f"客户端脚本: {client_script}")
            print(f"脚本存在: {os.path.exists(client_script)}")

            # 使用超时执行PyInstaller
            try:
                result = subprocess.run(cmd, capture_output=True, text=True,
                                      cwd=os.getcwd(), timeout=300)  # 5分钟超时
            except subprocess.TimeoutExpired:
                return False, "PyInstaller执行超时（超过5分钟），可能是环境问题", None

            if result.returncode == 0:
                exe_file = os.path.join(dist_dir, f"{client_name}.exe")
                if os.path.exists(exe_file):
                    return True, "exe打包成功", exe_file
                else:
                    return False, "exe文件未生成", None
            else:
                error_msg = result.stderr if result.stderr else result.stdout
                print(f"PyInstaller错误输出: {error_msg}")
                return False, f"PyInstaller执行失败: {error_msg[:200]}...", None

        except Exception as e:
            import traceback
            traceback.print_exc()
            return False, f"exe打包过程中发生错误: {e}", None

    def build_client_exe_async(self, client_dir, client_name, client_id):
        """异步打包客户端为exe，显示进度条"""
        import tkinter as tk
        from tkinter import ttk
        import threading

        # 检查是否有可用的Tk根窗口
        try:
            root = tk._default_root
            if root is None:
                # 没有GUI环境，直接调用同步方法
                print("没有GUI环境，使用同步exe生成...")
                return self.build_client_exe(client_dir, client_name, client_id)
        except:
            # 没有GUI环境，直接调用同步方法
            print("没有GUI环境，使用同步exe生成...")
            return self.build_client_exe(client_dir, client_name, client_id)

        # 创建进度窗口
        progress_window = tk.Toplevel()
        progress_window.title("正在生成exe客户端")
        progress_window.geometry("500x200")
        progress_window.resizable(False, False)
        progress_window.transient()
        progress_window.grab_set()

        # 居中显示
        progress_window.update_idletasks()
        x = (progress_window.winfo_screenwidth() // 2) - (500 // 2)
        y = (progress_window.winfo_screenheight() // 2) - (200 // 2)
        progress_window.geometry(f"500x200+{x}+{y}")

        # 进度界面
        main_frame = tk.Frame(progress_window, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = tk.Label(main_frame, text="正在生成exe客户端", font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 10))

        # 状态标签
        status_label = tk.Label(main_frame, text="正在检查PyInstaller...", font=("Arial", 10))
        status_label.pack(pady=(0, 10))

        # 进度条
        progress_bar = ttk.Progressbar(main_frame, mode='indeterminate', length=400)
        progress_bar.pack(pady=(0, 10))
        progress_bar.start()

        # 详细信息文本框
        detail_frame = tk.Frame(main_frame)
        detail_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        detail_text = tk.Text(detail_frame, height=6, width=60, font=("Consolas", 8))
        scrollbar = tk.Scrollbar(detail_frame, orient=tk.VERTICAL, command=detail_text.yview)
        detail_text.configure(yscrollcommand=scrollbar.set)

        detail_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 结果变量
        result = {"success": False, "message": "", "path": None}

        def update_status(text):
            """更新状态标签"""
            status_label.config(text=text)
            progress_window.update()

        def add_detail(text):
            """添加详细信息"""
            detail_text.insert(tk.END, text + "\n")
            detail_text.see(tk.END)
            progress_window.update()

        def build_exe_thread():
            """在后台线程中执行exe打包"""
            try:
                add_detail("开始exe打包过程...")

                # 调用原有的exe打包方法
                success, message, path = self.build_client_exe(client_dir, client_name, client_id,
                                                             update_status, add_detail)

                result["success"] = success
                result["message"] = message
                result["path"] = path

                # 更新UI
                progress_bar.stop()
                if success:
                    progress_bar.config(mode='determinate', value=100)
                    update_status("exe生成完成！")
                    add_detail(f"✓ exe文件生成成功: {path}")
                else:
                    update_status("exe生成失败")
                    add_detail(f"✗ exe生成失败: {message}")

                # 3秒后关闭窗口
                progress_window.after(3000, progress_window.destroy)

            except Exception as e:
                result["success"] = False
                result["message"] = f"exe打包过程中发生错误: {e}"

                progress_bar.stop()
                update_status("exe生成失败")
                add_detail(f"✗ 发生错误: {e}")

                # 3秒后关闭窗口
                progress_window.after(3000, progress_window.destroy)

        # 启动后台线程
        thread = threading.Thread(target=build_exe_thread)
        thread.daemon = True
        thread.start()

        # 等待窗口关闭
        progress_window.wait_window()

        return result["success"], result["message"], result["path"]

    def build_client_exe(self, client_dir, client_name, client_id, status_callback=None, detail_callback=None):
        """使用PyInstaller将客户端打包成exe"""
        try:
            if status_callback:
                status_callback("正在检查PyInstaller...")
            if detail_callback:
                detail_callback("检查PyInstaller是否可用...")

            # 检查PyInstaller是否可用
            try:
                result = subprocess.run([sys.executable, "-m", "PyInstaller", "--version"],
                                      capture_output=True, check=True, text=True)
                if detail_callback:
                    detail_callback(f"✓ PyInstaller可用: {result.stdout.strip()}")
            except (subprocess.CalledProcessError, FileNotFoundError):
                return False, "PyInstaller未安装或不可用", None

            # 客户端主程序路径（使用绝对路径）
            client_script = os.path.abspath(os.path.join(client_dir, "fme_client.py"))
            if not os.path.exists(client_script):
                return False, f"客户端主程序文件不存在: {client_script}", None

            if detail_callback:
                detail_callback(f"客户端脚本: {client_script}")

            # 创建临时工作目录
            temp_build_dir = tempfile.mkdtemp()
            if detail_callback:
                detail_callback(f"临时构建目录: {temp_build_dir}")

            try:
                if status_callback:
                    status_callback("正在准备打包参数...")

                # 构建PyInstaller命令
                exe_name = f"{client_name}_v1.0"
                output_dir = os.path.join(temp_build_dir, "dist")

                cmd = [
                    sys.executable, "-m", "PyInstaller",
                    "--onefile",                    # 打包成单个exe文件
                    "--windowed",                   # 不显示控制台窗口
                    "--name", exe_name,             # exe文件名
                    "--distpath", output_dir,       # 输出目录
                    "--workpath", os.path.join(temp_build_dir, "build"),  # 工作目录
                    "--specpath", temp_build_dir,   # spec文件目录
                    "--clean",                      # 清理临时文件
                    "--noconfirm",                  # 不询问覆盖
                ]

                # 不打包模型文件到exe中，因为模型文件已经在客户端系统路径/models下面
                # models_dir = os.path.abspath(os.path.join(client_dir, "models"))
                # if os.path.exists(models_dir):
                #     cmd.extend(["--add-data", f"{models_dir};models"])
                #     if detail_callback:
                #         detail_callback(f"添加模型目录: {models_dir}")
                if detail_callback:
                    detail_callback("跳过模型文件打包（模型文件已在客户端系统路径）")

                license_file = os.path.abspath(os.path.join(client_dir, "license.lic"))
                if os.path.exists(license_file):
                    cmd.extend(["--add-data", f"{license_file};."])
                    if detail_callback:
                        detail_callback(f"添加许可证文件: {license_file}")

                # 添加客户端配置文件
                config_file = os.path.abspath(os.path.join(client_dir, "client_config.json"))
                if os.path.exists(config_file):
                    cmd.extend(["--add-data", f"{config_file};."])
                    if detail_callback:
                        detail_callback(f"添加配置文件: {config_file}")

                # 添加加密模块
                encryption_file = os.path.abspath(os.path.join(client_dir, "encryption.py"))
                if os.path.exists(encryption_file):
                    cmd.extend(["--hidden-import", "encryption"])
                    if detail_callback:
                        detail_callback(f"添加加密模块: {encryption_file}")

                # 添加主程序文件
                cmd.append(client_script)

                if detail_callback:
                    detail_callback(f"PyInstaller命令: {' '.join(cmd[:5])}...")

                if status_callback:
                    status_callback("正在执行PyInstaller打包...")

                # 执行打包命令
                result = subprocess.run(
                    cmd,
                    cwd=temp_build_dir,
                    capture_output=True,
                    text=True,
                    timeout=300  # 5分钟超时
                )

                if result.returncode == 0:
                    if status_callback:
                        status_callback("打包完成，正在处理输出文件...")

                    # 打包成功，移动exe文件
                    exe_file = os.path.join(output_dir, f"{exe_name}.exe")
                    if os.path.exists(exe_file):
                        # 移动到客户端目录
                        final_exe_path = os.path.join(self.clients_dir, f"{exe_name}.exe")
                        shutil.move(exe_file, final_exe_path)

                        if detail_callback:
                            detail_callback(f"✓ exe文件生成成功: {final_exe_path}")
                            detail_callback(f"文件大小: {os.path.getsize(final_exe_path)} 字节")

                        return True, "exe打包成功", final_exe_path
                    else:
                        error_msg = "exe文件未生成"
                        if detail_callback:
                            detail_callback(f"✗ {error_msg}")
                        return False, error_msg, None
                else:
                    error_msg = f"PyInstaller执行失败:\n{result.stderr}"
                    if detail_callback:
                        detail_callback(f"✗ PyInstaller执行失败")
                        detail_callback(f"错误信息: {result.stderr}")
                    return False, error_msg, None

            finally:
                # 清理临时目录
                try:
                    shutil.rmtree(temp_build_dir)
                except:
                    pass

        except subprocess.TimeoutExpired:
            error_msg = "打包超时（超过5分钟）"
            if detail_callback:
                detail_callback(f"✗ {error_msg}")
            return False, error_msg, None
        except Exception as e:
            error_msg = f"打包过程中发生错误: {e}"
            if detail_callback:
                detail_callback(f"✗ {error_msg}")
            return False, error_msg, None

    def get_client_list(self):
        """获取已生成的客户端列表"""
        clients = []
        if os.path.exists(self.clients_dir):
            for item in os.listdir(self.clients_dir):
                if item.endswith('.zip'):
                    clients.append(item)
        return clients
