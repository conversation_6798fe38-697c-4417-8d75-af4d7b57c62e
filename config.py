"""
FME模型管理工具配置文件
"""
import os
import sys
import json
from pathlib import Path
from json_encoder import SecureJsonManager

# 应用基础配置
APP_NAME = "FME模型管理工具"
APP_VERSION = "1.0.0"
APP_AUTHOR = "FME Tools"

# 默认路径配置 - 使用相对路径
DEFAULT_FME_PATH = r"C:\Program Files\FME\fme.exe"
DEFAULT_MODELS_DIR = "models"  # 相对路径
DEFAULT_OUTPUT_DIR = "output"  # 相对路径
DEFAULT_TEMP_DIR = "temp"  # 相对路径
DEFAULT_CLIENTS_DIR = "clients"  # 相对路径

# 加密配置
ENCRYPTION_KEY_FILE = "encryption.key"
ENCRYPTED_EXTENSION = ".fmw_encrypted"

# 分发客户端配置
CLIENT_CONFIG_FILE = "client_config.json"
LICENSE_FILE = "license.json"

# 支持的文件类型
SUPPORTED_FMW_EXTENSIONS = [".fmw"]
SUPPORTED_ARCHIVE_EXTENSIONS = [".zip", ".rar", ".7z"]

# UI配置
WINDOW_SIZE = "1200x800"
THEME = "cosmo"  # ttkbootstrap主题

def get_app_directory():
    """获取应用程序目录"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe，使用exe所在目录
        return os.path.dirname(sys.executable)
    else:
        # 在开发环境下，使用main.py所在目录
        return os.path.dirname(os.path.abspath(__file__))


class Config:
    """配置管理类"""

    def __init__(self):
        # 获取应用程序目录
        self.app_directory = get_app_directory()

        # 使用安全JSON管理器
        self.json_manager = SecureJsonManager()

        # 配置文件名（不使用.json扩展名，将被编码为.dat）
        self.config_name = "app_config"

        self.config = self.load_config()
        
    def load_config(self):
        """加载配置文件"""
        default_config = {
            "app_name": APP_NAME,
            "app_version": APP_VERSION,
            "app_author": APP_AUTHOR,
            "fme_path": DEFAULT_FME_PATH,
            "models_dir": DEFAULT_MODELS_DIR,
            "output_dir": DEFAULT_OUTPUT_DIR,
            "temp_dir": DEFAULT_TEMP_DIR,
            "clients_dir": DEFAULT_CLIENTS_DIR,
            "theme": THEME,
            "window_size": WINDOW_SIZE,
            "auto_backup": True,
            "max_recent_files": 10,
            "recent_files": [],
            "workspace_dir": os.path.join(os.path.expanduser("~"), "FME_Workspace")
        }

        # 使用安全JSON管理器加载配置
        loaded_config = self.json_manager.load_config(self.config_name, default_config)

        # 如果加载的配置与默认配置不同，说明是首次运行或配置文件丢失
        if loaded_config == default_config:
            print("首次运行或配置文件不存在，已创建默认配置")
            self.save_config_data(loaded_config)

        return loaded_config
    
    def save_config(self):
        """保存配置文件"""
        self.save_config_data(self.config)

    def save_config_data(self, config_data):
        """保存配置数据"""
        try:
            success = self.json_manager.save_config(config_data, self.config_name)
            if not success:
                print("保存配置文件失败")
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get(self, key, default=None):
        """获取配置项"""
        return self.config.get(key, default)
    
    def set(self, key, value):
        """设置配置项"""
        self.config[key] = value
        self.save_config()
    
    def get_absolute_path(self, relative_path):
        """将相对路径转换为基于应用程序目录的绝对路径"""
        if os.path.isabs(relative_path):
            return relative_path
        return os.path.join(self.app_directory, relative_path)

    def ensure_directories(self):
        """确保必要的目录存在"""
        dirs = [
            self.get("models_dir"),
            self.get("output_dir"),
            self.get("temp_dir"),
            self.get("clients_dir")
        ]

        for dir_path in dirs:
            if dir_path:
                # 转换为绝对路径
                abs_path = self.get_absolute_path(dir_path)
                if not os.path.exists(abs_path):
                    try:
                        os.makedirs(abs_path, exist_ok=True)
                        print(f"创建目录: {abs_path}")
                    except Exception as e:
                        print(f"创建目录失败 {abs_path}: {e}")

    def get_app_directory(self):
        """获取应用程序目录"""
        return self.app_directory

# 全局配置实例
config = Config()
