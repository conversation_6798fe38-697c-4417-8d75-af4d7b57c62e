"""
完整功能测试脚本
测试所有核心功能是否正常工作
"""
import os
import sys
import tempfile
import shutil
from datetime import datetime, timedelta

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_fme_path_selection():
    """测试FME路径选择功能"""
    print("1. 测试FME路径选择功能...")
    
    try:
        from config import config
        
        # 测试默认FME路径
        default_path = config.get("fme_path")
        print(f"   默认FME路径: {default_path}")
        
        # 测试设置FME路径
        test_path = r"C:\Program Files\FME\fme.exe"
        config.set("fme_path", test_path)
        
        # 验证设置
        saved_path = config.get("fme_path")
        assert saved_path == test_path, "FME路径设置失败"
        
        print("   ✓ FME路径选择功能正常")
        return True
        
    except Exception as e:
        print(f"   ✗ FME路径选择功能失败: {e}")
        return False

def test_fme_execution():
    """测试FME调用功能"""
    print("2. 测试FME调用功能...")
    
    try:
        from fmw_runner import FMWRunner
        
        # 创建FMW运行器
        runner = FMWRunner("dummy_fme_path")
        
        # 测试命令构建
        test_params = {
            "INPUT_FILE": "test_input.shp",
            "OUTPUT_DIR": "test_output",
            "COORDINATE_SYSTEM": "EPSG:4326"
        }
        
        cmd = runner.build_command("test.fmw", test_params)
        
        # 验证命令
        assert "dummy_fme_path" in cmd
        assert "test.fmw" in cmd
        assert "--INPUT_FILE" in cmd
        assert "test_input.shp" in cmd
        
        print("   ✓ FME调用功能正常")
        return True
        
    except Exception as e:
        print(f"   ✗ FME调用功能失败: {e}")
        return False

def test_fmw_encryption():
    """测试FMW加密功能"""
    print("3. 测试FMW加密功能...")
    
    try:
        from encryption import FMWEncryption
        
        # 创建加密器
        encryption = FMWEncryption()
        
        # 创建测试文件
        test_content = "这是一个测试FMW文件内容"
        with tempfile.NamedTemporaryFile(mode='w', suffix='.fmw', delete=False) as f:
            f.write(test_content)
            test_file = f.name
        
        try:
            # 测试加密
            encrypted_file = encryption.encrypt_file(test_file)
            assert os.path.exists(encrypted_file), "加密文件未生成"
            
            # 测试解密
            decrypted_file = encryption.decrypt_file(encrypted_file)
            assert os.path.exists(decrypted_file), "解密文件未生成"
            
            # 验证内容
            with open(decrypted_file, 'r') as f:
                decrypted_content = f.read()
            assert decrypted_content == test_content, "解密内容不匹配"
            
            print("   ✓ FMW加密功能正常")
            return True
            
        finally:
            # 清理文件
            for file_path in [test_file, encrypted_file, decrypted_file]:
                if 'file_path' in locals() and os.path.exists(file_path):
                    try:
                        os.unlink(file_path)
                    except:
                        pass
        
    except Exception as e:
        print(f"   ✗ FMW加密功能失败: {e}")
        return False

def test_license_management():
    """测试许可证管理功能"""
    print("4. 测试许可证管理功能...")
    
    try:
        from encryption import LicenseManager
        
        # 创建许可证管理器
        license_manager = LicenseManager()
        
        # 测试机器码生成
        machine_id = license_manager.get_machine_id()
        assert machine_id and len(machine_id) > 0, "机器码生成失败"
        
        # 测试许可证生成
        expire_date = datetime.now() + timedelta(days=30)
        license_data = license_manager.generate_license(
            expire_date=expire_date,
            max_uses=100,
            allowed_machines=[machine_id]
        )
        
        assert license_data is not None, "许可证生成失败"
        assert license_data.get("license_id"), "许可证ID缺失"
        assert license_data.get("max_uses") == 100, "使用次数设置错误"
        
        # 测试许可证验证
        valid, message = license_manager.validate_license(license_data)
        assert valid, f"许可证验证失败: {message}"
        
        # 测试许可证保存和加载
        with tempfile.NamedTemporaryFile(suffix='.lic', delete=False) as f:
            license_file = f.name
        
        try:
            # 保存许可证
            success = license_manager.save_license(license_data, license_file)
            assert success, "许可证保存失败"
            
            # 加载许可证
            loaded_license = license_manager.load_license(license_file)
            assert loaded_license is not None, "许可证加载失败"
            assert loaded_license.get("license_id") == license_data.get("license_id"), "许可证ID不匹配"
            
        finally:
            if os.path.exists(license_file):
                os.unlink(license_file)
        
        print("   ✓ 许可证管理功能正常")
        return True
        
    except Exception as e:
        print(f"   ✗ 许可证管理功能失败: {e}")
        return False

def test_client_generation():
    """测试客户端生成功能"""
    print("5. 测试客户端生成功能...")
    
    try:
        from client_generator import ClientGenerator
        from model_manager import ModelManager
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        
        try:
            # 设置临时目录
            from config import config
            original_clients_dir = config.get("clients_dir")
            original_models_dir = config.get("models_dir")
            
            config.set("clients_dir", temp_dir)
            config.set("models_dir", temp_dir)
            
            # 创建客户端生成器
            generator = ClientGenerator()
            
            # 测试客户端模板创建
            template = generator.create_client_template()
            assert template and len(template) > 0, "客户端模板创建失败"
            
            # 测试客户端列表获取
            clients = generator.get_client_list()
            assert isinstance(clients, list), "客户端列表获取失败"
            
            # 恢复原始配置
            config.set("clients_dir", original_clients_dir)
            config.set("models_dir", original_models_dir)
            
        finally:
            # 清理临时目录
            shutil.rmtree(temp_dir, ignore_errors=True)
        
        print("   ✓ 客户端生成功能正常")
        return True
        
    except Exception as e:
        print(f"   ✗ 客户端生成功能失败: {e}")
        return False

def test_parameter_mapping():
    """测试参数映射功能"""
    print("6. 测试参数映射功能...")
    
    try:
        from parse_fmw import parse_fmw_parameters
        
        # 检查是否有测试FMW文件
        test_fmw = "fmw参数解析.fmw"
        if os.path.exists(test_fmw):
            # 测试参数解析
            result = parse_fmw_parameters(test_fmw, debug=False)
            assert result is not None, "参数解析失败"
            
            parameters = result.get('parameters', [])
            print(f"   解析到 {len(parameters)} 个参数")
            
            # 验证参数结构
            for param in parameters[:3]:  # 检查前3个参数
                assert param.get('name'), "参数名称缺失"
                assert param.get('info'), "参数信息缺失"
                
                param_info = param['info']
                assert param_info.get('type'), "参数类型缺失"
        else:
            print("   跳过参数映射测试（测试文件不存在）")
        
        print("   ✓ 参数映射功能正常")
        return True
        
    except Exception as e:
        print(f"   ✗ 参数映射功能失败: {e}")
        return False

def test_ui_components():
    """测试UI组件功能"""
    print("7. 测试UI组件功能...")
    
    try:
        import tkinter as tk
        import ttkbootstrap as ttk_bs
        
        # 测试基本UI组件
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 测试各种组件
        frame = ttk_bs.Frame(root)
        label = ttk_bs.Label(frame, text="测试标签")
        entry = ttk_bs.Entry(frame)
        button = ttk_bs.Button(frame, text="测试按钮")
        combo = ttk_bs.Combobox(frame, values=["选项1", "选项2"])
        
        # 测试主窗口类导入
        from main_window import MainWindow
        
        root.destroy()
        
        print("   ✓ UI组件功能正常")
        return True
        
    except Exception as e:
        print(f"   ✗ UI组件功能失败: {e}")
        return False

def test_license_generator():
    """测试注册机功能"""
    print("8. 测试注册机功能...")
    
    try:
        # 测试许可证生成器导入
        from license_generator import LicenseGenerator
        
        print("   ✓ 注册机功能正常")
        return True
        
    except Exception as e:
        print(f"   ✗ 注册机功能失败: {e}")
        return False

def run_complete_test():
    """运行完整功能测试"""
    print("=" * 60)
    print("FME模型管理工具完整功能测试")
    print("=" * 60)
    
    tests = [
        ("FME路径选择", test_fme_path_selection),
        ("FME调用", test_fme_execution),
        ("FMW加密", test_fmw_encryption),
        ("许可证管理", test_license_management),
        ("客户端生成", test_client_generation),
        ("参数映射", test_parameter_mapping),
        ("UI组件", test_ui_components),
        ("注册机", test_license_generator)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"   ✗ {test_name}测试异常: {e}")
        print()
    
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有功能测试通过！")
        print("\n✅ 核心功能状态:")
        print("   • FME目录选择 - 已实现")
        print("   • 调用FME - 已实现")
        print("   • FMW加密 - 已实现")
        print("   • 许可分发 - 已实现")
        print("   • 注册机 - 已实现")
        print("   • 参数映射 - 已实现")
        print("   • 图形界面 - 已实现")
        print("   • 客户端生成 - 已实现")
        
        print("\n🚀 启动方式:")
        print("   主程序: python main.py")
        print("   注册机: python license_generator.py")
        print("   参数演示: python parameter_mapping_demo.py")
        
        return True
    else:
        print("⚠️ 部分功能测试失败，请检查相关模块")
        return False

if __name__ == "__main__":
    success = run_complete_test()
    
    if success:
        print("\n" + "=" * 60)
        print("所有核心功能已完整实现并测试通过！")
        print("=" * 60)
    
    sys.exit(0 if success else 1)
