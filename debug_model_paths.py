"""
调试模型路径问题
"""
import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def debug_model_paths():
    """调试模型路径问题"""
    try:
        from model_manager import ModelManager
        from config import config
        
        print("1. 初始化管理器...")
        model_manager = ModelManager()
        
        print("2. 检查应用程序目录...")
        app_dir = config.get_app_directory()
        print(f"   应用程序目录: {app_dir}")
        
        print("3. 获取所有模型...")
        all_models = model_manager.get_all_models()
        print(f"   找到 {len(all_models)} 个模型")
        
        for model_id, model_info in all_models.items():
            print(f"\n模型ID: {model_id}")
            print(f"   名称: {model_info.get('name', 'N/A')}")
            print(f"   存储的file_path: '{model_info.get('file_path', 'N/A')}'")
            print(f"   file_path长度: {len(model_info.get('file_path', ''))}")
            print(f"   file_path字节: {repr(model_info.get('file_path', ''))}")
            
            # 检查路径转换
            stored_path = model_info.get('file_path', '')
            if stored_path:
                print(f"   是否绝对路径: {os.path.isabs(stored_path)}")
                
                try:
                    abs_path = model_manager.get_absolute_path(stored_path)
                    print(f"   转换后绝对路径: '{abs_path}'")
                    print(f"   绝对路径长度: {len(abs_path)}")
                    print(f"   绝对路径字节: {repr(abs_path)}")
                    print(f"   文件存在: {os.path.exists(abs_path)}")
                    
                    # 检查路径组成部分
                    print(f"   路径分解:")
                    print(f"     dirname: '{os.path.dirname(abs_path)}'")
                    print(f"     basename: '{os.path.basename(abs_path)}'")
                    
                    # 检查目录是否存在
                    dir_path = os.path.dirname(abs_path)
                    print(f"   目录存在: {os.path.exists(dir_path)}")
                    
                    if os.path.exists(dir_path):
                        files = os.listdir(dir_path)
                        print(f"   目录中的文件: {files}")
                    
                except Exception as e:
                    print(f"   路径转换失败: {e}")
                    import traceback
                    traceback.print_exc()
        
        print("\n4. 检查config.get_absolute_path方法...")
        test_path = "models\\test\\file.txt"
        result = config.get_absolute_path(test_path)
        print(f"   测试路径: '{test_path}'")
        print(f"   转换结果: '{result}'")
        
    except Exception as e:
        print(f"调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_model_paths()
