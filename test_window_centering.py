"""
测试窗口居中效果
"""
import sys
import os
import tkinter as tk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def center_window_test(window, parent=None, width=None, height=None):
    """测试版本的窗口居中函数，带调试信息"""
    print(f"居中窗口: {window.title()}")
    
    window.update_idletasks()
    
    # 获取窗口尺寸
    if width and height:
        window.geometry(f"{width}x{height}")
        print(f"  设置尺寸: {width}x{height}")
    
    window_width = window.winfo_reqwidth()
    window_height = window.winfo_reqheight()
    print(f"  窗口尺寸: {window_width}x{window_height}")
    
    if parent:
        # 相对于父窗口居中
        parent_x = parent.winfo_rootx()
        parent_y = parent.winfo_rooty()
        parent_width = parent.winfo_width()
        parent_height = parent.winfo_height()
        
        print(f"  父窗口位置: {parent_x}, {parent_y}")
        print(f"  父窗口尺寸: {parent_width}x{parent_height}")
        
        x = parent_x + (parent_width // 2) - (window_width // 2)
        y = parent_y + (parent_height // 2) - (window_height // 2)
        
        print(f"  计算位置: {x}, {y}")
        
        # 确保窗口不会超出屏幕边界
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        x = max(0, min(x, screen_width - window_width))
        y = max(0, min(y, screen_height - window_height))
        
        print(f"  调整后位置: {x}, {y}")
    else:
        # 屏幕居中
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        x = (screen_width // 2) - (window_width // 2)
        y = (screen_height // 2) - (window_height // 2)
        
        print(f"  屏幕居中位置: {x}, {y}")
    
    window.geometry(f"+{x}+{y}")
    print(f"  最终几何: {window.geometry()}")

def test_dialog_centering():
    """测试对话框居中"""
    # 创建主窗口
    style = ttk_bs.Style(theme="cosmo")
    root = style.master
    root.title("主窗口测试")
    root.geometry("800x600")
    
    # 主窗口居中
    center_window_test(root)
    
    def test_simple_dialog():
        """测试简单对话框"""
        dialog = tk.Toplevel(root)
        dialog.title("测试对话框")
        dialog.transient(root)
        dialog.grab_set()
        
        # 添加一些内容
        frame = ttk_bs.Frame(dialog, padding=20)
        frame.pack(fill=BOTH, expand=True)
        
        label = ttk_bs.Label(frame, text="这是一个测试对话框", font=("Arial", 12))
        label.pack(pady=10)
        
        button = ttk_bs.Button(frame, text="关闭", command=dialog.destroy)
        button.pack(pady=10)
        
        # 居中显示
        center_window_test(dialog, root, 400, 200)
    
    def test_model_import_dialog():
        """测试模型导入对话框"""
        try:
            from dialogs import ModelImportDialog
            dialog = ModelImportDialog(root, None)
        except Exception as e:
            print(f"模型导入对话框测试失败: {e}")
    
    def test_settings_dialog():
        """测试设置对话框"""
        try:
            from dialogs import SettingsDialog
            dialog = SettingsDialog(root)
        except Exception as e:
            print(f"设置对话框测试失败: {e}")
    
    def test_client_create_dialog():
        """测试客户端创建对话框"""
        try:
            from dialogs import ClientCreateDialog
            dialog = ClientCreateDialog(root)
        except Exception as e:
            print(f"客户端创建对话框测试失败: {e}")
    
    # 创建测试按钮
    button_frame = ttk_bs.Frame(root, padding=20)
    button_frame.pack(fill=X)
    
    ttk_bs.Button(button_frame, text="测试简单对话框", 
                 command=test_simple_dialog).pack(side=LEFT, padx=5)
    
    ttk_bs.Button(button_frame, text="测试模型导入对话框", 
                 command=test_model_import_dialog).pack(side=LEFT, padx=5)
    
    ttk_bs.Button(button_frame, text="测试设置对话框", 
                 command=test_settings_dialog).pack(side=LEFT, padx=5)
    
    ttk_bs.Button(button_frame, text="测试客户端创建对话框", 
                 command=test_client_create_dialog).pack(side=LEFT, padx=5)
    
    # 添加说明
    info_label = ttk_bs.Label(root, text="点击按钮测试各种对话框的居中效果\n观察控制台输出的调试信息", 
                             font=("Arial", 10), justify=CENTER)
    info_label.pack(pady=20)
    
    root.mainloop()

if __name__ == "__main__":
    test_dialog_centering()
