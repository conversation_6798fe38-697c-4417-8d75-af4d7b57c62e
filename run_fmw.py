@app.route('/api/run_fme', methods=['POST'])
def run_fme():
    # 进程窗口显示/隐藏逻辑由config_cache['fme_visibility']控制，需重启后端服务后生效
    try:
        data = request.get_json()
        username = request.headers.get('X-Username')
        
        if not username:
            return jsonify({
                'success': False,
                'message': '未提供用户名'
            }), 401
            
        logger.info(f"\n=== 提交FME任务 ===")
        logger.info(f"接收到的完整数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        # 获取任务ID
        task_id = data.get('task_id')
        if not task_id:
            return jsonify({
                'success': False,
                'message': '未提供任务ID'
            }), 400
        
        # 获取FME路径和参数
        fmw_path = data.get('fmw_path')
        params = data.get('params', {})
        fmw_id = data.get('fmw_id')
        
        logger.info(f"接收到的FMW路径: {fmw_path}")
        logger.info(f"当前工作目录: {current_dir}")
        logger.info(f"接收到的参数: {json.dumps(params, ensure_ascii=False, indent=2)}")
        
        # 检查文件是否存在
        if not fmw_path:
            logger.error("FMW路径为空")
            return jsonify({'success': False, 'message': 'FMW路径为空'})
            
        # 转换为绝对路径
        if not os.path.isabs(fmw_path):
            # 如果是相对路径，转换为绝对路径
            # 处理路径中的斜杠
            relative_path = fmw_path.replace('\\', '/').lstrip('/')
            # 构建完整的FMW文件路径
            fmw_path = os.path.normpath(os.path.join(current_dir, relative_path))
            logger.info(f"转换后的绝对路径: {fmw_path}")
        
        if not os.path.exists(fmw_path):
            logger.error(f"FMW文件不存在: {fmw_path}")
            return jsonify({'success': False, 'message': f'FMW文件不存在: {fmw_path}'})

        # 使用传入的task_id作为输出目录名
        output_dir = os.path.join(MODELS_DIR, fmw_id, 'output', task_id)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            logger.info(f"创建输出目录: {output_dir}")

        # 解析FMW文件获取参数信息
        try:
            fmw_params = parse_fmw_parameters(fmw_path)
            logger.info(f"解析到的FMW参数: {json.dumps(fmw_params, ensure_ascii=False, indent=2)}")
        except Exception as e:
            logger.error(f"解析FMW参数失败: {str(e)}")
            return jsonify({'success': False, 'message': f'解析FMW参数失败: {str(e)}'})

        if not fmw_params:
            logger.error("解析FMW参数失败")
            return jsonify({'success': False, 'message': '解析FMW参数失败'})

        # 查找输出参数名（access_mode为write的参数）
        output_param_name = None
        parameters_dict = fmw_params.get('parameters_dict', {})
        if not isinstance(parameters_dict, dict):
            logger.error(f"参数字典格式错误: {type(parameters_dict)}")
            return jsonify({'success': False, 'message': '参数字典格式错误'})
            
        for param_name, param_info in parameters_dict.items():
            if isinstance(param_info, dict) and param_info.get('access_mode') == 'write':
                output_param_name = param_name
                break

        if not output_param_name:
            logger.error("未找到输出参数")
            return jsonify({'success': False, 'message': '未找到输出参数'})

        # 添加任务记录
        task_record_data = {
            'task_id': task_id,
            'model_id': fmw_id,
            'model_name': data.get('fmw_name'),
            'username': username,
            'status': 'pending',
            'created_at': datetime.now().isoformat(),
            'time_consuming': 0,
            'file_size': '0MB',
            'file_name': '',
            'project': data.get('project', ''),
            'params': json.dumps(params) if isinstance(params, dict) else params,
            'up_nums': data.get('up_nums', 1)  # 添加文件数量，默认为1
        }
        
        # 直接调用 add_task_record 函数添加任务记录
        try:
            result = add_task_record(task_record_data)
            if not result.get_json().get('success'):
                logger.error(f"添加任务记录失败: {result.get_json().get('message')}")
                return jsonify({
                    'success': False,
                    'message': f'添加任务记录失败: {result.get_json().get("message")}'
                }), 500
                
            logger.info(f"任务记录添加成功: {task_id}")
        except Exception as e:
            logger.error(f"添加任务记录时发生错误: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'添加任务记录时发生错误: {str(e)}'
            }), 500
                
        # === 处理 save_path 路径，确保为绝对路径，且只出现一次 ===
        save_path = params.get('save_path')
        if save_path:
            # 如果是相对路径，根据环境选择基准路径
            if not os.path.isabs(save_path):
                if getattr(sys, 'frozen', False):
                    # 如果是打包后的exe，使用exe所在目录
                    base_dir = os.path.dirname(sys.executable)
                else:
                    # 在开发环境下，使用app.py所在目录
                    base_dir = os.path.dirname(os.path.abspath(__file__))
                save_path = os.path.normpath(os.path.join(base_dir, save_path))
                params['save_path'] = save_path

        # 构建FME命令行参数
        cmd = [FME_PATH, fmw_path]
        # 处理所有参数
        for key, value in params.items():
            cmd.extend([f'--{key}', str(value)])
        # 添加输出目录参数，但对于特定FMW不重复添加
        if output_param_name and fmw_id not in ['cad2gis', 'coordinatetransformation', 'quality']:
            cmd.extend([f'--{output_param_name}', output_dir])
        logger.info(f"构建的FME命令: {' '.join(cmd)}")

        # 将任务添加到Redis队列
        task_data = {
            'task_id': task_id,
            'args': {
                'cmd': cmd,
                'fmw_id': fmw_id,
                'fmw_name': data.get('fmw_name'),
                'username': username,
                'output_dir': output_dir
            }
        }
        
        if redis_manager.add_task(task_id, task_data):
            logger.info(f"任务已添加到Redis队列: {task_id}")
            return jsonify({
                'success': True,
                'message': '任务已提交到队列',
                'task_id': task_id
            })
        else:
            logger.error("Redis未连接，无法添加任务到队列")
            return jsonify({
                'success': False,
                'message': 'Redis未连接，无法添加任务到队列'
            })
            
    except Exception as e:
        logger.error(f"提交任务失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'提交任务失败: {str(e)}'
        }), 500
    