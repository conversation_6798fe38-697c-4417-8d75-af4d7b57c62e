"""
简单的路径调试
"""
import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

print("=== 路径调试 ===")

print("1. 基础路径信息:")
print(f"   当前工作目录: {os.getcwd()}")
print(f"   脚本目录: {current_dir}")
print(f"   sys.executable: {sys.executable}")
print(f"   sys.frozen: {getattr(sys, 'frozen', False)}")

print("\n2. 导入config模块:")
try:
    from config import config, get_app_directory
    
    app_dir = get_app_directory()
    print(f"   get_app_directory(): {app_dir}")
    print(f"   config.app_directory: {config.app_directory}")
    
    print("\n3. 测试路径拼接:")
    test_relative = "models\\e4488878d5848380\\model_cb8f68c5.dat.enc"
    result = config.get_absolute_path(test_relative)
    print(f"   相对路径: {test_relative}")
    print(f"   绝对路径: {result}")
    print(f"   路径长度: {len(result)}")
    print(f"   路径字节: {repr(result)}")
    
    print("\n4. 检查路径组成:")
    print(f"   app_directory: '{config.app_directory}'")
    print(f"   app_directory 长度: {len(config.app_directory)}")
    print(f"   app_directory 字节: {repr(config.app_directory)}")
    
    # 手动拼接测试
    manual_join = os.path.join(config.app_directory, test_relative)
    print(f"   手动拼接结果: {manual_join}")
    print(f"   是否相同: {result == manual_join}")
    
except Exception as e:
    print(f"   导入失败: {e}")
    import traceback
    traceback.print_exc()

print("\n5. 导入model_manager:")
try:
    from model_manager import ModelManager
    
    model_manager = ModelManager()
    all_models = model_manager.get_all_models()
    
    for model_id, model_info in all_models.items():
        print(f"\n   模型 {model_id}:")
        stored_path = model_info.get('file_path', '')
        print(f"     存储的路径: '{stored_path}'")
        print(f"     路径字节: {repr(stored_path)}")
        
        if stored_path:
            abs_path = model_manager.get_absolute_path(stored_path)
            print(f"     转换后路径: '{abs_path}'")
            print(f"     文件存在: {os.path.exists(abs_path)}")
            
            # 检查目录
            dir_path = os.path.dirname(abs_path)
            print(f"     目录: '{dir_path}'")
            print(f"     目录存在: {os.path.exists(dir_path)}")
            
            if os.path.exists(dir_path):
                files = os.listdir(dir_path)
                print(f"     目录文件: {files}")

except Exception as e:
    print(f"   导入失败: {e}")
    import traceback
    traceback.print_exc()
