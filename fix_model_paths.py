"""
修复模型路径问题
"""
import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def fix_model_paths():
    """修复模型路径问题"""
    try:
        from model_manager import ModelManager
        from config import config
        
        print("1. 初始化管理器...")
        model_manager = ModelManager()
        
        print("2. 获取所有模型...")
        all_models = model_manager.get_all_models()
        print(f"   找到 {len(all_models)} 个模型")
        
        fixed_count = 0
        
        for model_id, model_info in all_models.items():
            print(f"\n检查模型: {model_id}")
            print(f"   名称: {model_info.get('name', 'N/A')}")
            
            stored_path = model_info.get('file_path', '')
            print(f"   索引中的路径: {stored_path}")
            
            if stored_path:
                # 获取绝对路径
                abs_path = model_manager.get_absolute_path(stored_path)
                print(f"   绝对路径: {abs_path}")
                print(f"   文件存在: {os.path.exists(abs_path)}")
                
                if not os.path.exists(abs_path):
                    print("   ❌ 文件不存在，尝试修复...")
                    
                    # 获取模型目录
                    model_dir = os.path.dirname(abs_path)
                    print(f"   模型目录: {model_dir}")
                    
                    if os.path.exists(model_dir):
                        # 列出目录中的文件
                        files = os.listdir(model_dir)
                        print(f"   目录中的文件: {files}")
                        
                        # 查找可能的模型文件
                        possible_files = []
                        for file in files:
                            if file.endswith('.enc') or file.endswith('.fmw') or file.endswith('.dat'):
                                possible_files.append(file)
                        
                        print(f"   可能的模型文件: {possible_files}")
                        
                        if len(possible_files) == 1:
                            # 只有一个可能的文件，更新路径
                            correct_file = possible_files[0]
                            correct_relative_path = os.path.join(os.path.dirname(stored_path), correct_file)
                            
                            print(f"   更新路径: {stored_path} -> {correct_relative_path}")
                            
                            # 更新模型信息
                            model_info['file_path'] = correct_relative_path
                            
                            # 如果是加密文件，也更新hidden_filename
                            if correct_file.endswith('.enc'):
                                hidden_filename = correct_file.replace('.enc', '')
                                model_info['hidden_filename'] = hidden_filename
                                print(f"   更新隐藏文件名: {hidden_filename}")
                            
                            fixed_count += 1
                            print("   ✅ 路径已修复")
                        
                        elif len(possible_files) > 1:
                            print(f"   ⚠️ 找到多个可能的文件，需要手动选择: {possible_files}")
                        else:
                            print("   ❌ 没有找到可能的模型文件")
                    else:
                        print("   ❌ 模型目录不存在")
                else:
                    print("   ✅ 文件存在，无需修复")
        
        if fixed_count > 0:
            print(f"\n3. 保存修复后的索引...")
            success = model_manager.save_models_index()
            if success:
                print(f"   ✅ 已修复 {fixed_count} 个模型的路径")
            else:
                print("   ❌ 保存索引失败")
        else:
            print("\n3. 没有需要修复的模型")
        
        print("\n4. 验证修复结果...")
        for model_id, model_info in all_models.items():
            stored_path = model_info.get('file_path', '')
            if stored_path:
                abs_path = model_manager.get_absolute_path(stored_path)
                exists = os.path.exists(abs_path)
                print(f"   模型 {model_id}: {exists}")
                if not exists:
                    print(f"     路径: {abs_path}")
        
    except Exception as e:
        print(f"修复过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_model_paths()
