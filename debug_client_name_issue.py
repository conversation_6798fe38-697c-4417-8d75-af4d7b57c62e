"""
调试客户端名称问题
"""
import sys
import os
import tkinter as tk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def debug_client_name_issue():
    """调试客户端名称问题"""
    # 创建主窗口
    style = ttk_bs.Style(theme="cosmo")
    root = style.master
    root.title("调试客户端名称问题")
    root.geometry("600x400")
    
    # 主窗口居中
    from dialogs import center_window
    center_window(root)
    
    def test_client_create_dialog():
        """测试客户端创建对话框"""
        try:
            from model_manager import ModelManager
            from client_generator import ClientGenerator
            from dialogs import ClientCreateDialog
            
            print("初始化管理器...")
            model_manager = ModelManager()
            client_generator = ClientGenerator()
            
            print("获取模型...")
            models = model_manager.get_all_models()
            if not models:
                tk.messagebox.showerror("错误", "没有找到任何模型")
                return
            
            print(f"找到 {len(models)} 个模型")
            print(f"模型列表: {list(models.keys())}")
            
            # 启动客户端创建对话框
            print("启动客户端创建对话框...")
            print(f"传递参数: root={root}, models={type(models)}, client_generator={type(client_generator)}")
            
            dialog = ClientCreateDialog(root, models, client_generator)
            print("客户端创建对话框已创建")
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            tk.messagebox.showerror("错误", f"启动客户端创建对话框失败：\n{e}")
    
    def test_direct_build_progress():
        """直接测试打包进度对话框"""
        try:
            from dialogs import ClientBuildProgressDialog
            
            def mock_build_function():
                print("模拟打包函数执行")
                import time
                time.sleep(2)
                return {
                    'success': True,
                    'message': '模拟打包成功',
                    'zip_path': 'test.zip'
                }
            
            test_client_name = "测试客户端名称"
            print(f"创建进度对话框，客户端名称: '{test_client_name}'")
            
            dialog = ClientBuildProgressDialog(root, test_client_name, mock_build_function)
            result = dialog.get_result()
            
            print(f"进度对话框结果: {result}")
            tk.messagebox.showinfo("结果", f"测试结果：\n{result}")
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            tk.messagebox.showerror("错误", f"测试进度对话框失败：\n{e}")
    
    # 创建界面
    main_frame = ttk_bs.Frame(root, padding=20)
    main_frame.pack(fill=BOTH, expand=True)
    
    title_label = ttk_bs.Label(main_frame, text="调试客户端名称问题", 
                              font=("Arial", 16, "bold"))
    title_label.pack(pady=20)
    
    info_label = ttk_bs.Label(main_frame, 
                             text="测试客户端名称传递问题\n"
                                  "观察控制台输出和对话框标题", 
                             font=("Arial", 10), justify=CENTER)
    info_label.pack(pady=10)
    
    button_frame = ttk_bs.Frame(main_frame)
    button_frame.pack(pady=30)
    
    ttk_bs.Button(button_frame, text="测试客户端创建对话框", 
                 command=test_client_create_dialog, bootstyle=PRIMARY,
                 width=25).pack(pady=10)
    
    ttk_bs.Button(button_frame, text="直接测试进度对话框", 
                 command=test_direct_build_progress, bootstyle=SECONDARY,
                 width=25).pack(pady=10)
    
    status_label = ttk_bs.Label(main_frame, 
                               text="点击按钮开始测试\n"
                                    "观察控制台输出，查找'参数'字符串的来源", 
                               font=("Arial", 9), foreground="gray")
    status_label.pack(pady=20)
    
    print("调试程序已启动")
    print("观察控制台输出，查找客户端名称被设置为'参数'的原因")
    
    root.mainloop()

if __name__ == "__main__":
    debug_client_name_issue()
