"""
测试UI加载的简单脚本
"""
import sys
import os
import traceback

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_imports():
    """测试导入"""
    print("测试导入...")
    
    try:
        import tkinter as tk
        print("✓ tkinter 导入成功")
    except Exception as e:
        print(f"✗ tkinter 导入失败: {e}")
        return False
    
    try:
        import ttkbootstrap as ttk_bs
        print("✓ ttkbootstrap 导入成功")
    except Exception as e:
        print(f"✗ ttkbootstrap 导入失败: {e}")
        return False
    
    try:
        from config import config
        print("✓ config 导入成功")
    except Exception as e:
        print(f"✗ config 导入失败: {e}")
        return False
    
    return True

def test_theme():
    """测试主题"""
    print("\n测试主题...")
    
    try:
        import ttkbootstrap as ttk_bs
        
        # 测试默认主题
        style = ttk_bs.Style(theme="cosmo")
        print("✓ cosmo 主题加载成功")
        
        # 测试其他主题
        available_themes = style.theme_names()
        print(f"可用主题: {available_themes}")
        
        return True
    except Exception as e:
        print(f"✗ 主题测试失败: {e}")
        traceback.print_exc()
        return False

def test_simple_window():
    """测试简单窗口"""
    print("\n测试简单窗口...")
    
    try:
        import tkinter as tk
        import ttkbootstrap as ttk_bs
        from ttkbootstrap.constants import *
        
        # 创建简单窗口
        style = ttk_bs.Style(theme="cosmo")
        root = style.master
        root.title("测试窗口")
        root.geometry("400x300")
        
        # 添加一些组件
        frame = ttk_bs.Frame(root, padding=20)
        frame.pack(fill=BOTH, expand=True)
        
        label = ttk_bs.Label(frame, text="这是一个测试窗口", font=("Arial", 14))
        label.pack(pady=20)
        
        button = ttk_bs.Button(frame, text="关闭", command=root.destroy, bootstyle=PRIMARY)
        button.pack(pady=10)
        
        # 居中显示
        root.update_idletasks()
        width = 400
        height = 300
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width // 2) - (width // 2)
        y = (screen_height // 2) - (height // 2)
        root.geometry(f"{width}x{height}+{x}+{y}")
        
        print("✓ 简单窗口创建成功")
        print("窗口将在3秒后自动关闭...")
        
        # 3秒后自动关闭
        root.after(3000, root.destroy)
        
        root.mainloop()
        return True
        
    except Exception as e:
        print(f"✗ 简单窗口测试失败: {e}")
        traceback.print_exc()
        return False

def test_main_window():
    """测试主窗口"""
    print("\n测试主窗口...")
    
    try:
        from main_window import MainWindow
        
        print("✓ MainWindow 类导入成功")
        
        # 尝试创建主窗口
        app = MainWindow()
        print("✓ MainWindow 实例创建成功")
        
        # 检查窗口是否正确初始化
        if hasattr(app, 'root') and app.root:
            print("✓ 主窗口根对象存在")
            
            # 检查窗口标题
            title = app.root.title()
            print(f"窗口标题: {title}")
            
            # 检查窗口几何
            geometry = app.root.geometry()
            print(f"窗口几何: {geometry}")
            
            # 显示窗口3秒
            print("主窗口将显示3秒...")
            app.root.after(3000, app.root.destroy)
            app.root.mainloop()
            
            return True
        else:
            print("✗ 主窗口根对象不存在")
            return False
            
    except Exception as e:
        print(f"✗ 主窗口测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("FME管理工具UI诊断测试")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("\n导入测试失败，请检查依赖包安装")
        return
    
    # 测试主题
    if not test_theme():
        print("\n主题测试失败，可能是ttkbootstrap版本问题")
        return
    
    # 测试简单窗口
    if not test_simple_window():
        print("\n简单窗口测试失败")
        return
    
    # 测试主窗口
    if not test_main_window():
        print("\n主窗口测试失败")
        return
    
    print("\n所有测试通过！")

if __name__ == "__main__":
    main()
