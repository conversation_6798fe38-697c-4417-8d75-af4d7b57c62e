"""
调试客户端名称传递问题
"""
import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def debug_client_creation():
    """调试客户端创建过程中的名称传递"""
    try:
        from model_manager import ModelManager
        from client_generator import ClientGenerator
        
        print("1. 初始化管理器...")
        model_manager = ModelManager()
        client_generator = ClientGenerator()
        
        print("2. 获取模型...")
        all_models = model_manager.get_all_models()
        if not all_models:
            print("没有找到任何模型")
            return
        
        # 选择第一个模型
        selected_models = [list(all_models.keys())[0]]
        print(f"选择的模型: {selected_models}")
        
        print("3. 准备客户端信息...")
        client_info = {
            'name': '调试客户端名称',
            'project': '调试项目',
            'user': '调试用户',
            'contact': '<EMAIL>',
            'description': '这是一个调试客户端'
        }
        
        print(f"客户端信息: {client_info}")
        print(f"客户端名称: '{client_info['name']}'")
        
        # 手动调用build_complete_client_package来调试
        from datetime import datetime
        import tempfile
        
        client_id = f"debug_{int(datetime.now().timestamp())}"
        client_dir = os.path.join(client_generator.clients_dir, client_id)
        client_name = client_info['name']
        
        print(f"4. 调试参数:")
        print(f"   client_dir: {client_dir}")
        print(f"   client_name: '{client_name}'")
        print(f"   client_id: {client_id}")
        print(f"   model_count: {len(selected_models)}")
        
        # 创建临时客户端目录用于测试
        os.makedirs(client_dir, exist_ok=True)
        
        # 创建一个简化的测试函数
        def test_build_function(progress_callback=None):
            print("测试打包函数被调用")
            if progress_callback:
                progress_callback(50, "测试进度更新")
            return {
                'success': True,
                'message': '测试成功',
                'zip_path': 'test.zip'
            }
        
        print("5. 测试进度对话框创建...")
        
        # 直接测试ClientBuildProgressDialog
        import tkinter as tk
        import ttkbootstrap as ttk_bs
        
        style = ttk_bs.Style(theme="cosmo")
        root = style.master
        root.title("调试主窗口")
        root.geometry("400x300")
        
        def start_debug_dialog():
            print(f"创建进度对话框，客户端名称: '{client_name}'")
            from dialogs import ClientBuildProgressDialog
            
            dialog = ClientBuildProgressDialog(root, client_name, test_build_function)
            result = dialog.get_result()
            print(f"对话框结果: {result}")
            
            # 清理测试目录
            import shutil
            if os.path.exists(client_dir):
                shutil.rmtree(client_dir)
        
        # 创建按钮来启动测试
        from dialogs import center_window
        center_window(root)
        
        frame = ttk_bs.Frame(root, padding=20)
        frame.pack(fill='both', expand=True)
        
        label = ttk_bs.Label(frame, text=f"调试客户端名称传递\n客户端名称: '{client_name}'", 
                            font=("Arial", 12), justify='center')
        label.pack(pady=20)
        
        button = ttk_bs.Button(frame, text="测试进度对话框", 
                              command=start_debug_dialog, bootstyle='primary')
        button.pack(pady=10)
        
        info_label = ttk_bs.Label(frame, 
                                 text="点击按钮查看进度对话框标题\n"
                                      "检查是否显示正确的客户端名称", 
                                 font=("Arial", 9), foreground="gray")
        info_label.pack(pady=10)
        
        print("调试界面已启动，点击按钮测试")
        root.mainloop()
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"调试过程中发生错误: {e}")

if __name__ == "__main__":
    debug_client_creation()
