"""
安装PyInstaller脚本
"""
import subprocess
import sys


def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    
    try:
        # 升级pip
        print("1. 升级pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True)
        
        # 安装PyInstaller
        print("2. 安装PyInstaller...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], 
                      check=True)
        
        # 验证安装
        print("3. 验证安装...")
        result = subprocess.run([sys.executable, "-m", "PyInstaller", "--version"], 
                              capture_output=True, text=True, check=True)
        
        print(f"✓ PyInstaller安装成功！版本: {result.stdout.strip()}")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ 安装失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 安装过程中发生错误: {e}")
        return False


if __name__ == "__main__":
    print("=== PyInstaller安装程序 ===\n")
    
    # 检查是否已安装
    try:
        result = subprocess.run([sys.executable, "-m", "PyInstaller", "--version"], 
                              capture_output=True, text=True, check=True)
        print(f"PyInstaller已安装，版本: {result.stdout.strip()}")
        print("无需重新安装。")
    except:
        # 未安装，开始安装
        success = install_pyinstaller()
        
        if success:
            print("\n" + "="*50)
            print("🎉 PyInstaller安装完成！")
            print("现在可以使用exe客户端生成功能了。")
            print("="*50)
        else:
            print("\n" + "="*50)
            print("❌ PyInstaller安装失败。")
            print("请手动运行: pip install pyinstaller")
            print("="*50)
