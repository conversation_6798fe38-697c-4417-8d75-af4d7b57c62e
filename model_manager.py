"""
FMW模型管理模块
"""
import os
import json
import shutil
import zipfile
import hashlib
from datetime import datetime
from json_encoder import SecureJsonManager
from pathlib import Path
from config import config
from parse_fmw import parse_fmw_parameters
from encryption import FMWEncryption
from database import DatabaseManager

class ModelManager:
    """FMW模型管理器"""
    
    def __init__(self):
        self.models_dir = config.get("models_dir")
        self.models_index_file = os.path.join(self.models_dir, "models_index")  # 不使用.json扩展名
        self.encryption = FMWEncryption()
        self.json_manager = SecureJsonManager()
        self.db = DatabaseManager()

        # 确保模型目录存在
        os.makedirs(self.models_dir, exist_ok=True)

        # 检查是否需要数据迁移
        self._check_and_migrate_data()

        # 从数据库加载模型索引（保持向后兼容）
        self.models_index = self.load_models_index()

    def _check_and_migrate_data(self):
        """检查并执行数据迁移"""
        # 检查是否存在旧的JSON数据但数据库中没有数据
        db_models = self.db.get_all_models()
        if not db_models and os.path.exists("models_index.dat"):
            print("检测到旧的模型数据，正在迁移到数据库...")
            try:
                from data_migration import DataMigration
                migration = DataMigration()
                migration.migrate_models()
                print("模型数据迁移完成")
            except Exception as e:
                print(f"数据迁移失败: {e}")

    def load_models_index(self):
        """加载模型索引"""
        # 使用安全JSON管理器加载编码的索引文件
        default_index = {"models": {}, "last_updated": datetime.now().isoformat()}
        models_index = self.json_manager.load_config("models_index", default_index)
        return models_index
    
    def save_models_index(self):
        """保存模型索引（同时保存到数据库和JSON）"""
        try:
            self.models_index["last_updated"] = datetime.now().isoformat()

            # 保存到数据库
            for model_id, model_info in self.models_index["models"].items():
                model_data = dict(model_info)
                model_data['id'] = model_id
                model_data['updated_at'] = datetime.now().isoformat()
                self.db.save_model(model_data)

            # 保持向后兼容，也保存到JSON
            return self.json_manager.save_config(self.models_index, "models_index")
        except Exception as e:
            print(f"保存模型索引失败: {e}")
            return False
    
    def calculate_file_hash(self, file_path):
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            print(f"计算文件哈希失败: {e}")
            return None
    
    def import_model(self, fmw_path, model_name=None, description="", project="默认项目", category="默认", auto_encrypt=True):
        """导入FMW模型（自动加密保护）"""
        if not os.path.exists(fmw_path):
            raise FileNotFoundError(f"FMW文件不存在: {fmw_path}")

        # 生成模型ID
        file_hash = self.calculate_file_hash(fmw_path)
        if not file_hash:
            raise Exception("无法计算文件哈希值")

        model_id = file_hash[:16]  # 使用哈希值前16位作为ID

        # 检查模型是否已存在
        if model_id in self.models_index["models"]:
            return False, f"模型已存在: {model_id}", model_id

        # 创建模型目录
        model_dir = os.path.join(self.models_dir, model_id)
        os.makedirs(model_dir, exist_ok=True)

        # 先解析FMW参数（在加密前）
        try:
            fmw_params = parse_fmw_parameters(fmw_path)
            parameters = fmw_params.get('parameters', []) if fmw_params else []
        except Exception as e:
            print(f"解析FMW参数失败: {e}")
            parameters = []

        # 生成隐藏的文件名（不包含.fmw扩展名）
        import uuid
        hidden_filename = f"model_{uuid.uuid4().hex[:8]}.dat"  # 使用.dat扩展名隐藏真实类型

        # 原始文件路径（未加密，但文件名已隐藏）
        original_file_path = os.path.join(model_dir, hidden_filename)
        # 加密文件路径
        encrypted_file_path = os.path.join(model_dir, hidden_filename + '.enc')

        if auto_encrypt:
            # 直接加密导入的FMW文件
            try:
                self.encryption.encrypt_file(fmw_path, encrypted_file_path)
                print(f"FMW文件已加密保存: {encrypted_file_path}")

                # 不保存原始文件，只保存加密文件
                model_file_path = encrypted_file_path
                is_encrypted = True

            except Exception as e:
                print(f"加密FMW文件失败: {e}")
                # 加密失败时保存原始文件
                shutil.copy2(fmw_path, original_file_path)
                model_file_path = original_file_path
                is_encrypted = False
        else:
            # 不加密，直接复制
            shutil.copy2(fmw_path, original_file_path)
            model_file_path = original_file_path
            is_encrypted = False
        
        # 创建模型信息 - 使用相对于应用程序目录的相对路径
        app_dir = config.get_app_directory()
        relative_file_path = os.path.relpath(model_file_path, app_dir)
        relative_original_path = os.path.relpath(original_file_path, app_dir) if auto_encrypt else relative_file_path
        relative_encrypted_path = os.path.relpath(encrypted_file_path, app_dir) if is_encrypted else None

        model_info = {
            "id": model_id,
            "name": model_name or os.path.splitext(os.path.basename(fmw_path))[0],
            "description": description,
            "project": project,
            "category": category,
            "original_filename": os.path.basename(fmw_path),  # 保存原始文件名
            "hidden_filename": hidden_filename,  # 隐藏的文件名
            "file_path": relative_file_path,  # 使用相对路径
            "original_file_path": relative_original_path,  # 使用相对路径
            "file_hash": file_hash,
            "file_size": os.path.getsize(model_file_path),
            "parameters": parameters,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "is_encrypted": is_encrypted,
            "encrypted_file_path": relative_encrypted_path,  # 使用相对路径
            "usage_count": 0,
            "last_used": None
        }
        
        # 添加到索引
        self.models_index["models"][model_id] = model_info
        self.save_models_index()
        
        return True, f"模型导入成功: {model_info['name']}", model_id
    
    def encrypt_model(self, model_id):
        """加密模型"""
        if model_id not in self.models_index["models"]:
            return False, "模型不存在"
        
        model_info = self.models_index["models"][model_id]
        
        if model_info["is_encrypted"]:
            return False, "模型已加密"
        
        try:
            # 加密文件
            encrypted_path = model_info["file_path"] + ".encrypted"
            self.encryption.encrypt_file(model_info["file_path"], encrypted_path)
            
            # 更新模型信息
            model_info["is_encrypted"] = True
            model_info["encrypted_file_path"] = encrypted_path
            model_info["updated_at"] = datetime.now().isoformat()
            
            self.save_models_index()
            return True, "模型加密成功"
            
        except Exception as e:
            return False, f"模型加密失败: {e}"
    
    def decrypt_model(self, model_id, output_path=None):
        """解密模型（运行时临时解密）"""
        if model_id not in self.models_index["models"]:
            return False, "模型不存在", None

        model_info = self.models_index["models"][model_id]

        if not model_info["is_encrypted"]:
            # 模型未加密，直接返回原文件路径
            return True, "模型未加密，直接使用", model_info["file_path"]

        try:
            if output_path is None:
                # 创建临时解密文件 - 使用绝对路径
                temp_dir = config.get("temp_dir")
                temp_dir_abs = self.get_absolute_path(temp_dir)
                os.makedirs(temp_dir_abs, exist_ok=True)
                output_path = os.path.join(temp_dir_abs, f"{model_id}_runtime.fmw")

            # 获取加密文件的绝对路径
            encrypted_file_path = self.get_absolute_path(model_info["file_path"])

            # 检查是否已有临时解密文件且是最新的
            if os.path.exists(output_path):
                # 检查文件修改时间，如果加密文件更新了，需要重新解密
                encrypted_mtime = os.path.getmtime(encrypted_file_path)
                decrypted_mtime = os.path.getmtime(output_path)

                if decrypted_mtime >= encrypted_mtime:
                    # 临时文件是最新的，直接使用
                    return True, "使用已解密的临时文件", output_path

            # 解密文件到临时位置
            self.encryption.decrypt_file(encrypted_file_path, output_path)

            print(f"模型已解密到临时文件: {output_path}")
            return True, "模型解密成功", output_path

        except Exception as e:
            return False, f"模型解密失败: {e}", None

    def get_runtime_fmw_path(self, model_id):
        """获取运行时FMW文件路径（自动处理加密/解密）"""
        model_info = self.get_model_info(model_id)
        if not model_info:
            return None

        if not model_info.get("is_encrypted", False):
            # 未加密模型，返回绝对路径
            relative_path = model_info["file_path"]
            return self.get_absolute_path(relative_path)

        # 加密模型，返回解密后的临时路径
        success, message, decrypted_path = self.decrypt_model(model_id)
        if success:
            return decrypted_path
        else:
            print(f"获取运行时FMW路径失败: {message}")
            return None
    
    def delete_model(self, model_id):
        """删除模型"""
        # 先从数据库获取模型信息
        model_info = self.get_model_info(model_id)
        if not model_info:
            return False, "模型不存在"

        try:
            # 删除模型目录 - 使用绝对路径
            relative_path = model_info["file_path"]
            absolute_path = self.get_absolute_path(relative_path)
            model_dir = os.path.dirname(absolute_path)
            if os.path.exists(model_dir):
                shutil.rmtree(model_dir)

            # 从数据库中删除
            self.db.delete_model(model_id)

            # 从JSON索引中删除（保持向后兼容）
            if model_id in self.models_index["models"]:
                del self.models_index["models"][model_id]
                self.save_models_index()

            return True, "模型删除成功"

        except Exception as e:
            return False, f"模型删除失败: {e}"
    
    def get_model_info(self, model_id):
        """获取模型信息"""
        # 优先从数据库获取
        db_model = self.db.get_model(model_id)
        if db_model:
            return db_model
        else:
            # 回退到JSON数据
            return self.models_index["models"].get(model_id)

    def get_all_models(self):
        """获取所有模型"""
        # 优先从数据库获取，如果数据库为空则从JSON获取
        db_models = self.db.get_all_models()
        if db_models:
            return db_models
        else:
            # 回退到JSON数据
            return self.models_index["models"]

    def get_absolute_path(self, relative_path):
        """将相对路径转换为绝对路径"""
        if os.path.isabs(relative_path):
            return relative_path
        return config.get_absolute_path(relative_path)

    def get_model_absolute_path(self, model_id):
        """获取模型的绝对路径"""
        model_info = self.get_model_info(model_id)
        if not model_info:
            return None

        file_path = model_info.get('file_path')
        if not file_path:
            return None

        return self.get_absolute_path(file_path)
    
    def search_models(self, keyword="", category=""):
        """搜索模型"""
        models = self.models_index["models"]
        results = {}
        
        for model_id, model_info in models.items():
            # 关键词搜索
            if keyword:
                if (keyword.lower() not in model_info["name"].lower() and 
                    keyword.lower() not in model_info["description"].lower()):
                    continue
            
            # 分类筛选
            if category and model_info["category"] != category:
                continue
            
            results[model_id] = model_info
        
        return results
    
    def update_model_usage(self, model_id):
        """更新模型使用统计"""
        if model_id in self.models_index["models"]:
            model_info = self.models_index["models"][model_id]
            model_info["usage_count"] = model_info.get("usage_count", 0) + 1
            model_info["last_used"] = datetime.now().isoformat()
            self.save_models_index()
    
    def export_model(self, model_id, export_path):
        """导出模型"""
        if model_id not in self.models_index["models"]:
            return False, "模型不存在"
        
        model_info = self.models_index["models"][model_id]
        
        try:
            # 创建导出包
            with zipfile.ZipFile(export_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 添加FMW文件
                zipf.write(model_info["file_path"], model_info["file_name"])
                
                # 添加加密文件（如果存在）
                if model_info["is_encrypted"] and os.path.exists(model_info["encrypted_file_path"]):
                    zipf.write(model_info["encrypted_file_path"], 
                              model_info["file_name"] + ".encrypted")
                
                # 添加模型信息
                model_info_json = json.dumps(model_info, ensure_ascii=False, indent=2)
                zipf.writestr("model_info.json", model_info_json)
            
            return True, f"模型导出成功: {export_path}"
            
        except Exception as e:
            return False, f"模型导出失败: {e}"
