"""
FME模型管理工具打包脚本
"""
import os
import sys
import shutil
import subprocess
import zipfile
from datetime import datetime

def create_executable():
    """使用PyInstaller创建可执行文件"""
    print("创建可执行文件...")
    
    # PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=FME模型管理工具",
        "--icon=icon.ico",  # 如果有图标文件
        "--add-data=client_template.py;.",
        "--add-data=README.md;.",
        "main.py"
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ 可执行文件创建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 创建可执行文件失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    except FileNotFoundError:
        print("✗ PyInstaller未安装，跳过可执行文件创建")
        print("要创建可执行文件，请运行: pip install pyinstaller")
        return False

def create_portable_package():
    """创建便携版包"""
    print("创建便携版包...")
    
    package_name = f"FME模型管理工具_v1.0.0_{datetime.now().strftime('%Y%m%d')}"
    package_dir = f"dist/{package_name}"
    
    # 创建包目录
    os.makedirs(package_dir, exist_ok=True)
    
    # 复制主要文件
    files_to_copy = [
        "main.py",
        "main_window.py",
        "config.py",
        "model_manager.py",
        "fmw_runner.py",
        "encryption.py",
        "client_generator.py",
        "dialogs.py",
        "parse_fmw.py",
        "client_template.py",
        "requirements.txt",
        "start.bat",
        "README.md",
        "demo.py"
    ]
    
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            shutil.copy2(file_name, package_dir)
            print(f"  复制: {file_name}")
        else:
            print(f"  跳过: {file_name} (文件不存在)")
    
    # 复制测试文件（如果存在）
    if os.path.exists("fmw参数解析.fmw"):
        shutil.copy2("fmw参数解析.fmw", package_dir)
        print("  复制: fmw参数解析.fmw")
    
    # 创建安装说明
    install_guide = f"""# FME模型管理工具安装说明

## 版本信息
- 版本: v1.0.0
- 打包时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 快速开始

### 方式一：使用启动脚本（推荐）
1. 双击运行 `start.bat`
2. 脚本会自动安装依赖并启动程序

### 方式二：手动安装
1. 确保已安装 Python 3.7+
2. 打开命令行，进入此目录
3. 运行: `pip install -r requirements.txt`
4. 运行: `python main.py`

## 功能演示
运行 `python demo.py` 查看功能演示

## 详细说明
请查看 README.md 文件获取完整使用说明
"""
    
    with open(os.path.join(package_dir, "安装说明.txt"), 'w', encoding='utf-8') as f:
        f.write(install_guide)
    
    print(f"✓ 便携版包创建完成: {package_dir}")
    return package_dir

def create_zip_package(package_dir):
    """创建ZIP压缩包"""
    print("创建ZIP压缩包...")
    
    zip_name = f"{os.path.basename(package_dir)}.zip"
    zip_path = os.path.join("dist", zip_name)
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, os.path.dirname(package_dir))
                zipf.write(file_path, arc_name)
                
    print(f"✓ ZIP包创建完成: {zip_path}")
    return zip_path

def create_installer_script():
    """创建安装脚本"""
    print("创建安装脚本...")
    
    installer_script = """@echo off
chcp 65001 >nul
title FME模型管理工具安装程序

echo ========================================
echo FME模型管理工具安装程序
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 检测到管理员权限
) else (
    echo 注意: 建议以管理员身份运行此安装程序
)

echo.
echo 正在安装FME模型管理工具...
echo.

:: 创建程序目录
set INSTALL_DIR=%ProgramFiles%\\FME模型管理工具
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
    echo 创建安装目录: %INSTALL_DIR%
)

:: 复制文件
echo 复制程序文件...
xcopy /E /I /Y . "%INSTALL_DIR%"

:: 创建桌面快捷方式
echo 创建桌面快捷方式...
set DESKTOP=%USERPROFILE%\\Desktop
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%DESKTOP%\\FME模型管理工具.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%INSTALL_DIR%\\start.bat" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%INSTALL_DIR%" >> CreateShortcut.vbs
echo oLink.Description = "FME模型管理工具" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs
cscript CreateShortcut.vbs >nul
del CreateShortcut.vbs

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 程序已安装到: %INSTALL_DIR%
echo 桌面快捷方式已创建
echo.
echo 双击桌面上的"FME模型管理工具"图标启动程序
echo.
pause
"""
    
    installer_path = "dist/install.bat"
    with open(installer_path, 'w', encoding='utf-8') as f:
        f.write(installer_script)
    
    print(f"✓ 安装脚本创建完成: {installer_path}")
    return installer_path

def main():
    """主函数"""
    print("=" * 60)
    print("FME模型管理工具打包程序")
    print("=" * 60)
    
    # 创建dist目录
    os.makedirs("dist", exist_ok=True)
    
    # 创建便携版包
    package_dir = create_portable_package()
    
    # 创建ZIP压缩包
    zip_path = create_zip_package(package_dir)
    
    # 创建安装脚本
    installer_path = create_installer_script()
    
    # 尝试创建可执行文件
    exe_created = create_executable()
    
    print("\n" + "=" * 60)
    print("打包完成！")
    print("=" * 60)
    
    print(f"\n生成的文件:")
    print(f"  便携版目录: {package_dir}")
    print(f"  ZIP压缩包: {zip_path}")
    print(f"  安装脚本: {installer_path}")
    
    if exe_created:
        exe_path = "dist/FME模型管理工具.exe"
        if os.path.exists(exe_path):
            print(f"  可执行文件: {exe_path}")
    
    print(f"\n分发说明:")
    print(f"  - 便携版: 解压ZIP包即可使用")
    print(f"  - 安装版: 运行install.bat进行安装")
    if exe_created:
        print(f"  - 单文件版: 直接运行exe文件")
    
    print(f"\n使用说明:")
    print(f"  1. 确保目标机器已安装Python 3.7+")
    print(f"  2. 确保目标机器已安装FME Desktop")
    print(f"  3. 首次运行时会自动安装依赖包")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n❌ 打包过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
