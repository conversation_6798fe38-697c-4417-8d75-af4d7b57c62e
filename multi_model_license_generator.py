"""
多模型许可证生成器
支持为多个FMW模型生成统一的许可证文件
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import os
import json
from datetime import datetime, timed<PERSON>ta

from encryption import LicenseManager
from model_manager import ModelManager
from database import DatabaseManager

# 导入居中函数
def center_window(window, parent=None, width=None, height=None):
    """窗口居中显示的通用函数"""
    # 设置窗口尺寸
    if width and height:
        window.geometry(f"{width}x{height}")
        # 强制更新窗口
        window.update_idletasks()
        window.update()

    # 更新窗口以获取正确的尺寸
    window.update_idletasks()

    # 获取窗口实际尺寸
    if width and height:
        # 使用指定的尺寸
        window_width = width
        window_height = height
    else:
        # 获取窗口当前尺寸
        window_width = window.winfo_width()
        window_height = window.winfo_height()

        # 如果获取的尺寸为0或太小，使用请求的尺寸
        if window_width <= 1:
            window_width = window.winfo_reqwidth()
        if window_height <= 1:
            window_height = window.winfo_reqheight()

        # 如果请求的尺寸也太小，使用默认尺寸
        if window_width <= 1:
            window_width = 400
        if window_height <= 1:
            window_height = 300

    if parent:
        # 确保父窗口信息是最新的
        parent.update_idletasks()

        # 相对于父窗口居中
        parent_x = parent.winfo_rootx()
        parent_y = parent.winfo_rooty()
        parent_width = parent.winfo_width()
        parent_height = parent.winfo_height()

        x = parent_x + (parent_width // 2) - (window_width // 2)
        y = parent_y + (parent_height // 2) - (window_height // 2)

        # 确保窗口不会超出屏幕边界
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()

        x = max(0, min(x, screen_width - window_width))
        y = max(0, min(y, screen_height - window_height))
    else:
        # 屏幕居中
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()

        x = (screen_width // 2) - (window_width // 2)
        y = (screen_height // 2) - (window_height // 2)

    # 设置窗口位置
    window.geometry(f"+{x}+{y}")

    # 如果指定了尺寸，确保尺寸正确设置
    if width and height:
        window.geometry(f"{width}x{height}+{x}+{y}")


class MultiModelLicenseGenerator:
    """多模型许可证生成器"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.license_manager = LicenseManager()
        self.model_manager = ModelManager()
        self.db = DatabaseManager()
        self.selected_models = {}  # {model_id: license_config}

        self.create_window()
        
    def create_window(self):
        """创建窗口"""
        if self.parent:
            self.window = tk.Toplevel(self.parent)
            self.window.transient(self.parent)
            self.window.grab_set()
        else:
            self.window = tk.Tk()

        self.window.title("多模型许可证生成器")
        self.window.resizable(True, True)

        # 创建界面
        self.create_widgets()

        # 居中显示
        center_window(self.window, self.parent, 900, 700)
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk_bs.Frame(self.window, padding=10)
        main_frame.pack(fill=BOTH, expand=True)
        
        # 标题
        title_label = ttk_bs.Label(
            main_frame, 
            text="多模型许可证生成器", 
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # 客户端信息框架
        client_frame = ttk_bs.LabelFrame(main_frame, text="客户端信息", padding=10)
        client_frame.pack(fill=X, pady=(0, 10))

        # 第一行：客户端名称和使用项目
        ttk_bs.Label(client_frame, text="客户端名称:").grid(row=0, column=0, sticky=W, padx=(0, 10))
        self.client_name_var = tk.StringVar()
        ttk_bs.Entry(client_frame, textvariable=self.client_name_var, width=25).grid(row=0, column=1, sticky=W, padx=(0, 20))

        ttk_bs.Label(client_frame, text="使用项目:").grid(row=0, column=2, sticky=W, padx=(0, 10))
        self.project_var = tk.StringVar()
        project_combo = ttk_bs.Combobox(client_frame, textvariable=self.project_var, width=22,
                                       values=["数据处理项目", "空间分析项目", "质量检查项目", "地图制图项目", "其他"])
        project_combo.grid(row=0, column=3, sticky=W)

        # 第二行：使用人和联系方式
        ttk_bs.Label(client_frame, text="使用人:").grid(row=1, column=0, sticky=W, padx=(0, 10), pady=(10, 0))
        self.user_var = tk.StringVar()
        ttk_bs.Entry(client_frame, textvariable=self.user_var, width=25).grid(row=1, column=1, sticky=W, padx=(0, 20), pady=(10, 0))

        ttk_bs.Label(client_frame, text="联系方式:").grid(row=1, column=2, sticky=W, padx=(0, 10), pady=(10, 0))
        self.contact_var = tk.StringVar()
        ttk_bs.Entry(client_frame, textvariable=self.contact_var, width=22).grid(row=1, column=3, sticky=W, pady=(10, 0))

        # 第三行：机器码
        ttk_bs.Label(client_frame, text="机器码:").grid(row=2, column=0, sticky=W, padx=(0, 10), pady=(10, 0))
        self.machine_id_var = tk.StringVar()
        ttk_bs.Entry(client_frame, textvariable=self.machine_id_var, width=50).grid(row=2, column=1, columnspan=3, sticky=W, pady=(10, 0))

        # 第四行：描述
        ttk_bs.Label(client_frame, text="描述:").grid(row=3, column=0, sticky=W, padx=(0, 10), pady=(10, 0))
        self.description_var = tk.StringVar()
        ttk_bs.Entry(client_frame, textvariable=self.description_var, width=50).grid(row=3, column=1, columnspan=3, sticky=W, pady=(10, 0))
        
        # 模型选择框架
        models_frame = ttk_bs.LabelFrame(main_frame, text="模型选择与配置", padding=10)
        models_frame.pack(fill=BOTH, expand=True, pady=(0, 10))
        
        # 可用模型列表
        left_frame = ttk_bs.Frame(models_frame)
        left_frame.pack(side=LEFT, fill=BOTH, expand=True, padx=(0, 10))
        
        ttk_bs.Label(left_frame, text="可用模型:").pack(anchor=W)
        
        # 模型列表
        self.models_listbox = tk.Listbox(left_frame, height=15)
        self.models_listbox.pack(fill=BOTH, expand=True, pady=(5, 0))
        
        # 加载模型列表
        self.load_models()
        
        # 中间按钮
        middle_frame = ttk_bs.Frame(models_frame)
        middle_frame.pack(side=LEFT, padx=10)
        
        ttk_bs.Button(middle_frame, text="添加 →", command=self.add_model).pack(pady=5)
        ttk_bs.Button(middle_frame, text="← 移除", command=self.remove_model).pack(pady=5)
        ttk_bs.Button(middle_frame, text="配置", command=self.configure_model).pack(pady=5)
        
        # 已选模型列表
        right_frame = ttk_bs.Frame(models_frame)
        right_frame.pack(side=LEFT, fill=BOTH, expand=True, padx=(10, 0))
        
        ttk_bs.Label(right_frame, text="已选模型:").pack(anchor=W)
        
        # 已选模型树形视图
        columns = ("模型名称", "过期时间", "使用次数")
        self.selected_tree = ttk.Treeview(right_frame, columns=columns, show="tree headings", height=15)
        
        # 设置列标题
        self.selected_tree.heading("#0", text="模型ID")
        for col in columns:
            self.selected_tree.heading(col, text=col)
            
        # 设置列宽
        self.selected_tree.column("#0", width=100)
        self.selected_tree.column("模型名称", width=150)
        self.selected_tree.column("过期时间", width=100)
        self.selected_tree.column("使用次数", width=80)
        
        self.selected_tree.pack(fill=BOTH, expand=True, pady=(5, 0))
        
        # 操作按钮框架
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.pack(fill=X, pady=(10, 0))
        
        ttk_bs.Button(button_frame, text="生成许可证文件", command=self.generate_license_file).pack(side=LEFT, padx=(0, 10))
        ttk_bs.Button(button_frame, text="清空选择", command=self.clear_selection).pack(side=LEFT, padx=(0, 10))
        ttk_bs.Button(button_frame, text="查看历史", command=self.show_history).pack(side=LEFT, padx=(0, 10))
        ttk_bs.Button(button_frame, text="关闭", command=self.window.destroy).pack(side=RIGHT)
        
    def load_models(self):
        """加载可用模型列表"""
        self.models_listbox.delete(0, tk.END)
        
        models = self.model_manager.get_all_models()
        for model_id, model_info in models.items():
            display_text = f"{model_info['name']} ({model_id})"
            self.models_listbox.insert(tk.END, display_text)
            
    def add_model(self):
        """添加模型到选择列表"""
        selection = self.models_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个模型")
            return
            
        # 获取选中的模型
        selected_text = self.models_listbox.get(selection[0])
        model_id = selected_text.split('(')[-1].rstrip(')')
        
        if model_id in self.selected_models:
            messagebox.showinfo("提示", "该模型已经添加")
            return
            
        # 获取模型信息
        models = self.model_manager.get_all_models()
        model_info = models.get(model_id)
        
        if not model_info:
            messagebox.showerror("错误", "模型信息不存在")
            return
            
        # 添加默认配置
        self.selected_models[model_id] = {
            'model_name': model_info['name'],
            'expire_days': None,
            'max_uses': None
        }
        
        self.update_selected_tree()
        
    def remove_model(self):
        """从选择列表移除模型"""
        selection = self.selected_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要移除的模型")
            return
            
        item = selection[0]
        model_id = self.selected_tree.item(item)['text']
        
        if model_id in self.selected_models:
            del self.selected_models[model_id]
            self.update_selected_tree()
            
    def configure_model(self):
        """配置模型许可证参数"""
        selection = self.selected_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要配置的模型")
            return
            
        item = selection[0]
        model_id = self.selected_tree.item(item)['text']
        
        if model_id not in self.selected_models:
            return
            
        # 打开配置对话框
        config_dialog = ModelLicenseConfigDialog(self.window, model_id, self.selected_models[model_id])
        if config_dialog.result:
            self.selected_models[model_id] = config_dialog.result
            self.update_selected_tree()
            
    def update_selected_tree(self):
        """更新已选模型树形视图"""
        # 清空现有项目
        for item in self.selected_tree.get_children():
            self.selected_tree.delete(item)
            
        # 添加已选模型
        for model_id, config in self.selected_models.items():
            expire_text = f"{config['expire_days']}天" if config['expire_days'] else "无限制"
            uses_text = str(config['max_uses']) if config['max_uses'] else "无限制"
            
            self.selected_tree.insert("", tk.END, text=model_id, values=(
                config['model_name'],
                expire_text,
                uses_text
            ))
            
    def clear_selection(self):
        """清空选择"""
        self.selected_models.clear()
        self.update_selected_tree()
        
    def generate_license_file(self):
        """生成许可证文件"""
        if not self.selected_models:
            messagebox.showwarning("警告", "请至少选择一个模型")
            return
            
        # 验证必填字段
        client_name = self.client_name_var.get().strip()
        project = self.project_var.get().strip()
        user = self.user_var.get().strip()
        machine_id = self.machine_id_var.get().strip()

        if not client_name:
            messagebox.showwarning("警告", "请输入客户端名称")
            return

        if not project:
            messagebox.showwarning("警告", "请选择使用项目")
            return

        if not user:
            messagebox.showwarning("警告", "请输入使用人")
            return

        if not machine_id:
            messagebox.showwarning("警告", "请输入机器码")
            return
            
        if len(machine_id) != 32:
            messagebox.showwarning("警告", "机器码格式不正确（应为32位十六进制字符串）")
            return
            
        try:
            # 为每个模型生成许可证
            model_licenses = {}
            
            for model_id, config in self.selected_models.items():
                expire_date = None
                if config['expire_days']:
                    expire_date = datetime.now() + timedelta(days=config['expire_days'])
                    
                license_data = self.license_manager.generate_model_license(
                    model_id=model_id,
                    model_name=config['model_name'],
                    expire_date=expire_date,
                    max_uses=config['max_uses'],
                    allowed_machines=[machine_id],
                    client_name=client_name,
                    project=project,
                    user=user,
                    contact=self.contact_var.get().strip(),
                    description=self.description_var.get().strip()
                )
                
                model_licenses[model_id] = license_data
                
            # 选择保存路径
            file_path = filedialog.asksaveasfilename(
                title="保存许可证文件",
                defaultextension=".lic",
                filetypes=[("许可证文件", "*.lic"), ("所有文件", "*.*")],
                initialfile=f"{client_name}_license.lic"
            )
            
            if file_path:
                # 保存多模型许可证文件
                success = self.license_manager.save_multi_model_license(model_licenses, file_path)
                
                if success:
                    # 保存历史记录
                    self.save_license_history(client_name, project, user, len(self.selected_models), "成功")
                    messagebox.showinfo("成功", f"许可证文件已生成: {file_path}")
                else:
                    # 保存失败记录
                    self.save_license_history(client_name, project, user, len(self.selected_models), "失败")
                    messagebox.showerror("错误", "生成许可证文件失败")
                    
        except Exception as e:
            # 保存失败记录
            self.save_license_history(client_name, project, user, len(self.selected_models), f"失败: {e}")
            messagebox.showerror("错误", f"生成许可证文件时发生错误: {e}")

    def save_license_history(self, client_name, project, user, model_count, status):
        """保存许可证生成历史到数据库"""
        try:
            # 保存到数据库
            history_data = {
                "created_at": datetime.now().isoformat(),
                "client_name": client_name,
                "project": project,
                "user": user,
                "contact": self.contact_var.get().strip(),
                "description": self.description_var.get().strip(),
                "model_count": model_count,
                "status": status
            }

            self.db.save_license_history(history_data)

            # 保持向后兼容，也保存到JSON
            history_file = "license_history.json"
            history = []
            if os.path.exists(history_file):
                with open(history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)

            # 添加新记录（使用字符串格式的时间）
            record = dict(history_data)
            record["created_at"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            history.append(record)

            # 保持最近100条记录
            if len(history) > 100:
                history = history[-100:]

            # 保存历史
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"保存许可证历史记录失败: {e}")

    def show_history(self):
        """显示许可证生成历史"""
        LicenseHistoryDialog(self.window)


class LicenseHistoryDialog:
    """许可证生成历史对话框"""

    def __init__(self, parent):
        self.db = DatabaseManager()

        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("许可证生成历史")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        center_window(self.dialog, parent, 900, 500)

        self.setup_ui()
        self.load_history()

        # 等待对话框关闭
        self.dialog.wait_window()

    def setup_ui(self):
        """设置界面"""
        main_frame = ttk_bs.Frame(self.dialog, padding=10)
        main_frame.pack(fill=BOTH, expand=True)

        ttk_bs.Label(main_frame, text="许可证生成历史:", font=("", 12, "bold")).pack(anchor=W, pady=(0, 10))

        # 创建历史列表
        columns = ("生成时间", "客户端名称", "使用项目", "使用人", "联系方式", "模型数量", "状态")
        self.history_tree = ttk_bs.Treeview(main_frame, columns=columns, show="headings", height=15)

        # 设置列标题
        for col in columns:
            self.history_tree.heading(col, text=col)

        # 设置列宽
        self.history_tree.column("生成时间", width=120)
        self.history_tree.column("客户端名称", width=120)
        self.history_tree.column("使用项目", width=100)
        self.history_tree.column("使用人", width=80)
        self.history_tree.column("联系方式", width=100)
        self.history_tree.column("模型数量", width=80)
        self.history_tree.column("状态", width=80)

        # 添加滚动条
        scrollbar = ttk_bs.Scrollbar(main_frame, orient=VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        list_frame = ttk_bs.Frame(main_frame)
        list_frame.pack(fill=BOTH, expand=True, pady=(0, 10))

        self.history_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)

        # 按钮框架
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.pack(fill=X)

        ttk_bs.Button(button_frame, text="刷新", bootstyle=SECONDARY,
                     command=self.load_history).pack(side=LEFT)
        ttk_bs.Button(button_frame, text="关闭", bootstyle=PRIMARY,
                     command=self.dialog.destroy).pack(side=RIGHT)

    def load_history(self):
        """加载历史记录"""
        # 清空现有项目
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)

        try:
            # 优先从数据库获取
            history = self.db.get_license_history()

            # 如果数据库为空，回退到JSON文件
            if not history:
                import os
                import json

                history_file = "license_history.json"
                if os.path.exists(history_file):
                    with open(history_file, 'r', encoding='utf-8') as f:
                        history = json.load(f)

                    # 按时间倒序排列
                    history = sorted(history, key=lambda x: x.get('created_at', ''), reverse=True)

            for record in history:
                # 格式化时间显示
                created_at = record.get("created_at", "")
                if "T" in created_at:  # ISO格式转换为显示格式
                    try:
                        from datetime import datetime
                        dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        created_at = dt.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        pass

                self.history_tree.insert("", tk.END, values=(
                    created_at,
                    record.get("client_name", ""),
                    record.get("project", ""),
                    record.get("user_name", record.get("user", "")),  # 兼容两种字段名
                    record.get("contact", ""),
                    record.get("model_count", 0),
                    record.get("status", "")
                ))

        except Exception as e:
            print(f"加载许可证历史记录失败: {e}")


class ModelLicenseConfigDialog:
    """模型许可证配置对话框"""
    
    def __init__(self, parent, model_id, current_config):
        self.parent = parent
        self.model_id = model_id
        self.current_config = current_config.copy()
        self.result = None
        
        self.create_dialog()
        
    def create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(f"配置模型许可证 - {self.model_id}")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # 居中显示
        center_window(self.dialog, self.parent, 400, 300)
        
        # 创建界面
        main_frame = ttk_bs.Frame(self.dialog, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # 过期时间设置
        expire_frame = ttk_bs.LabelFrame(main_frame, text="过期时间设置", padding=10)
        expire_frame.pack(fill=X, pady=(0, 10))
        
        self.enable_expire_var = tk.BooleanVar(value=bool(self.current_config.get('expire_days')))
        ttk_bs.Checkbutton(
            expire_frame, 
            text="启用过期时间限制", 
            variable=self.enable_expire_var,
            command=self.toggle_expire
        ).pack(anchor=W)
        
        expire_input_frame = ttk_bs.Frame(expire_frame)
        expire_input_frame.pack(fill=X, pady=(10, 0))
        
        ttk_bs.Label(expire_input_frame, text="过期天数:").pack(side=LEFT)
        self.expire_days_var = tk.StringVar(value=str(self.current_config.get('expire_days', 30)))
        self.expire_days_entry = ttk_bs.Entry(expire_input_frame, textvariable=self.expire_days_var, width=10)
        self.expire_days_entry.pack(side=LEFT, padx=(10, 0))
        
        # 使用次数设置
        usage_frame = ttk_bs.LabelFrame(main_frame, text="使用次数设置", padding=10)
        usage_frame.pack(fill=X, pady=(0, 10))
        
        self.enable_usage_var = tk.BooleanVar(value=bool(self.current_config.get('max_uses')))
        ttk_bs.Checkbutton(
            usage_frame, 
            text="启用使用次数限制", 
            variable=self.enable_usage_var,
            command=self.toggle_usage
        ).pack(anchor=W)
        
        usage_input_frame = ttk_bs.Frame(usage_frame)
        usage_input_frame.pack(fill=X, pady=(10, 0))
        
        ttk_bs.Label(usage_input_frame, text="最大使用次数:").pack(side=LEFT)
        self.max_uses_var = tk.StringVar(value=str(self.current_config.get('max_uses', 100)))
        self.max_uses_entry = ttk_bs.Entry(usage_input_frame, textvariable=self.max_uses_var, width=10)
        self.max_uses_entry.pack(side=LEFT, padx=(10, 0))
        
        # 按钮
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.pack(fill=X, pady=(20, 0))
        
        ttk_bs.Button(button_frame, text="确定", command=self.save_config).pack(side=RIGHT, padx=(10, 0))
        ttk_bs.Button(button_frame, text="取消", command=self.dialog.destroy).pack(side=RIGHT)
        
        # 初始状态
        self.toggle_expire()
        self.toggle_usage()
        
    def toggle_expire(self):
        """切换过期时间设置"""
        if self.enable_expire_var.get():
            self.expire_days_entry.config(state=tk.NORMAL)
        else:
            self.expire_days_entry.config(state=tk.DISABLED)
            
    def toggle_usage(self):
        """切换使用次数设置"""
        if self.enable_usage_var.get():
            self.max_uses_entry.config(state=tk.NORMAL)
        else:
            self.max_uses_entry.config(state=tk.DISABLED)
            
    def save_config(self):
        """保存配置"""
        try:
            config = {
                'model_name': self.current_config['model_name'],
                'expire_days': None,
                'max_uses': None
            }
            
            # 过期时间
            if self.enable_expire_var.get():
                expire_days = int(self.expire_days_var.get())
                if expire_days <= 0:
                    raise ValueError("过期天数必须大于0")
                config['expire_days'] = expire_days
                
            # 使用次数
            if self.enable_usage_var.get():
                max_uses = int(self.max_uses_var.get())
                if max_uses <= 0:
                    raise ValueError("使用次数必须大于0")
                config['max_uses'] = max_uses
                
            self.result = config
            self.dialog.destroy()
            
        except ValueError as e:
            messagebox.showerror("错误", f"配置参数错误: {e}")


if __name__ == "__main__":
    app = MultiModelLicenseGenerator()
    app.window.mainloop()
