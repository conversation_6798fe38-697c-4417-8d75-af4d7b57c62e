"""
数据迁移模块 - 将JSON数据迁移到SQLite数据库
"""
import os
import json
from datetime import datetime
from database import DatabaseManager


class DataMigration:
    """数据迁移类"""
    
    def __init__(self):
        self.db = DatabaseManager()
    
    def migrate_all_data(self):
        """迁移所有数据"""
        print("开始数据迁移...")
        
        # 迁移模型数据
        self.migrate_models()
        
        # 迁移任务数据
        self.migrate_tasks()
        
        # 迁移客户端历史
        self.migrate_client_history()
        
        # 迁移许可证历史
        self.migrate_license_history()
        
        # 迁移应用配置
        self.migrate_app_config()
        
        print("数据迁移完成！")
    
    def migrate_models(self):
        """迁移模型数据"""
        print("迁移模型数据...")
        
        # 检查是否存在加密的模型索引文件
        if os.path.exists("models_index.dat"):
            try:
                # 尝试使用现有的ModelManager来读取数据
                from model_manager import ModelManager
                model_manager = ModelManager()
                models = model_manager.get_all_models()
                
                migrated_count = 0
                for model_id, model_info in models.items():
                    # 确保必要字段存在
                    model_data = {
                        'id': model_id,
                        'name': model_info.get('name', ''),
                        'description': model_info.get('description', ''),
                        'project': model_info.get('project', ''),
                        'category': model_info.get('category', '默认'),
                        'original_filename': model_info.get('original_filename', ''),
                        'hidden_filename': model_info.get('hidden_filename', ''),
                        'file_path': model_info.get('file_path', ''),
                        'original_file_path': model_info.get('original_file_path', ''),
                        'file_hash': model_info.get('file_hash', ''),
                        'file_size': model_info.get('file_size', 0),
                        'parameters': model_info.get('parameters', []),
                        'created_at': model_info.get('created_at', datetime.now().isoformat()),
                        'updated_at': model_info.get('updated_at', datetime.now().isoformat()),
                        'is_encrypted': model_info.get('is_encrypted', False),
                        'encrypted_file_path': model_info.get('encrypted_file_path', ''),
                        'usage_count': model_info.get('usage_count', 0),
                        'last_used': model_info.get('last_used', '')
                    }
                    
                    if self.db.save_model(model_data):
                        migrated_count += 1
                
                print(f"  成功迁移 {migrated_count} 个模型")
                
            except Exception as e:
                print(f"  迁移模型数据失败: {e}")
        else:
            print("  没有找到模型数据文件")
    
    def migrate_tasks(self):
        """迁移任务数据"""
        print("迁移任务数据...")
        
        if os.path.exists("task_history.json"):
            try:
                with open("task_history.json", 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                tasks = data.get('tasks', [])
                migrated_count = 0
                
                for task_data in tasks:
                    if self.db.save_task(task_data):
                        migrated_count += 1
                
                print(f"  成功迁移 {migrated_count} 个任务")
                
            except Exception as e:
                print(f"  迁移任务数据失败: {e}")
        else:
            print("  没有找到任务历史文件")
    
    def migrate_client_history(self):
        """迁移客户端历史"""
        print("迁移客户端历史...")
        
        if os.path.exists("client_history.json"):
            try:
                with open("client_history.json", 'r', encoding='utf-8') as f:
                    history_list = json.load(f)
                
                migrated_count = 0
                for history_data in history_list:
                    if self.db.save_client_history(history_data):
                        migrated_count += 1
                
                print(f"  成功迁移 {migrated_count} 条客户端历史记录")
                
            except Exception as e:
                print(f"  迁移客户端历史失败: {e}")
        else:
            print("  没有找到客户端历史文件")
    
    def migrate_license_history(self):
        """迁移许可证历史"""
        print("迁移许可证历史...")
        
        if os.path.exists("license_history.json"):
            try:
                with open("license_history.json", 'r', encoding='utf-8') as f:
                    history_list = json.load(f)
                
                migrated_count = 0
                for history_data in history_list:
                    if self.db.save_license_history(history_data):
                        migrated_count += 1
                
                print(f"  成功迁移 {migrated_count} 条许可证历史记录")
                
            except Exception as e:
                print(f"  迁移许可证历史失败: {e}")
        else:
            print("  没有找到许可证历史文件")
    
    def migrate_app_config(self):
        """迁移应用配置"""
        print("迁移应用配置...")
        
        try:
            from config import config
            
            # 迁移一些重要的配置项
            config_items = [
                'app_version',
                'theme',
                'window_size',
                'fme_path',
                'models_dir',
                'clients_dir',
                'output_dir'
            ]
            
            migrated_count = 0
            for key in config_items:
                value = config.get(key)
                if value is not None:
                    if self.db.set_config(key, value):
                        migrated_count += 1
            
            print(f"  成功迁移 {migrated_count} 个配置项")
            
        except Exception as e:
            print(f"  迁移应用配置失败: {e}")
    
    def backup_json_files(self):
        """备份原有的JSON文件"""
        print("备份原有JSON文件...")
        
        backup_dir = "json_backup"
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        json_files = [
            "models_index.dat",
            "task_history.json", 
            "client_history.json",
            "license_history.json"
        ]
        
        for file_name in json_files:
            if os.path.exists(file_name):
                backup_path = os.path.join(backup_dir, file_name)
                try:
                    import shutil
                    shutil.copy2(file_name, backup_path)
                    print(f"  已备份: {file_name} -> {backup_path}")
                except Exception as e:
                    print(f"  备份失败 {file_name}: {e}")


def run_migration():
    """运行数据迁移"""
    migration = DataMigration()
    
    # 备份原有文件
    migration.backup_json_files()
    
    # 执行迁移
    migration.migrate_all_data()
    
    print("\n数据迁移完成！")
    print("原有JSON文件已备份到 json_backup 目录")
    print("现在可以使用新的数据库系统了")


if __name__ == "__main__":
    run_migration()
